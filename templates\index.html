<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 剪映自动混剪工具 v2.1.0</title>
    <style>
        :root {
            /* 现代化色彩系统 */
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --secondary-light: #a78bfa;
            --accent-color: #10b981;
            --accent-light: #34d399;
            --warning-color: #f59e0b;
            --warning-light: #fbbf24;
            --error-color: #ef4444;
            --error-light: #f87171;
            --success-color: #10b981;
            --info-color: #3b82f6;

            /* 文字颜色 */
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #9ca3af;
            --text-inverse: #ffffff;

            /* 背景颜色 */
            --bg-primary: #f9fafb;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f3f4f6;
            --bg-dark: #111827;
            --bg-overlay: rgba(0, 0, 0, 0.5);

            /* 边框和阴影 */
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --border-dark: #d1d5db;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* 布局尺寸 */
            --sidebar-width: 280px;
            --header-height: 64px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;

            /* 动画和过渡 */
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease-out;
            --transition-slow: all 0.5s ease-in-out;

            /* 渐变色 */
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--success-color));
            --gradient-warm: linear-gradient(135deg, #fbbf24, #f59e0b);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            font-size: 14px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 现代化页面加载器 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: var(--transition-slow);
            backdrop-filter: blur(10px);
        }

        .page-loader.hidden {
            opacity: 0;
            pointer-events: none;
            transform: scale(0.95);
        }

        .loader-content {
            text-align: center;
            color: var(--text-inverse);
            animation: fadeInUp 0.6s ease-out;
        }

        .loader-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-top: 3px solid var(--text-inverse);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
            box-shadow: var(--shadow);
        }

        .loader-content h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .loader-content p {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 主布局 */
        .app-container {
            display: flex;
            height: 100vh;
        }

        /* 现代化侧边栏 */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            transform: translateX(0);
            transition: var(--transition);
            box-shadow: var(--shadow-lg);
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--border-light);
            background: var(--gradient-primary);
            color: var(--text-inverse);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .sidebar-header h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 6px;
            position: relative;
            z-index: 1;
            letter-spacing: -0.5px;
        }

        .sidebar-header .version {
            font-size: 13px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            font-weight: 500;
        }

        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            padding: 16px 0;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) transparent;
        }

        .sidebar-nav::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 2px;
        }

        .nav-item {
            margin: 3px 12px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 14px 16px;
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            cursor: pointer;
            user-select: none;
            position: relative;
            font-weight: 500;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 0;
            background: var(--primary-color);
            border-radius: 0 2px 2px 0;
            transition: var(--transition);
        }

        .nav-link:hover {
            background: var(--bg-tertiary);
            color: var(--primary-color);
            transform: translateX(2px);
        }

        .nav-link:hover::before {
            height: 20px;
        }

        .nav-link.active {
            background: var(--primary-color);
            color: var(--text-inverse);
            box-shadow: var(--shadow);
        }

        .nav-link.active::before {
            height: 100%;
            background: rgba(255, 255, 255, 0.3);
        }

        .nav-link .icon {
            margin-right: 12px;
            font-size: 18px;
            width: 22px;
            text-align: center;
            transition: var(--transition);
        }

        .nav-link:hover .icon {
            transform: scale(1.1);
        }

        .nav-link .text {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-link .arrow {
            font-size: 12px;
            transition: var(--transition);
            opacity: 0.7;
        }

        .nav-link.expanded .arrow {
            transform: rotate(90deg);
            opacity: 1;
        }

        /* 现代化二级菜单 */
        .sub-nav {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--bg-tertiary);
            margin: 4px 12px 8px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-light);
        }

        .sub-nav.expanded {
            max-height: 400px;
            box-shadow: var(--shadow-sm);
        }

        .sub-nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px 12px 40px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 13px;
            transition: var(--transition);
            border-radius: var(--border-radius);
            margin: 4px 8px;
            position: relative;
            font-weight: 500;
        }

        .sub-nav-link::before {
            content: '•';
            position: absolute;
            left: 24px;
            color: var(--text-tertiary);
            font-size: 16px;
            transition: var(--transition);
        }

        .sub-nav-link:hover {
            background: var(--bg-secondary);
            color: var(--primary-color);
            transform: translateX(4px);
            box-shadow: var(--shadow-sm);
        }

        .sub-nav-link:hover::before {
            color: var(--primary-color);
            transform: scale(1.2);
        }

        .sub-nav-link.active {
            background: var(--primary-light);
            color: var(--text-inverse);
            box-shadow: var(--shadow);
        }

        .sub-nav-link.active::before {
            color: var(--text-inverse);
            content: '▶';
            font-size: 12px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            display: flex;
            flex-direction: column;
            transition: var(--transition);
        }

        .main-content.sidebar-collapsed {
            margin-left: 0;
        }

        /* 顶部栏 */
        .top-bar {
            height: var(--header-height);
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 20px;
            justify-content: space-between;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .breadcrumb-item {
            margin-right: 8px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--border-color);
        }

        .top-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: var(--transition);
        }

        .menu-toggle:hover {
            background: var(--bg-primary);
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .content-section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 现代化卡片样式 */
        .card {
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            opacity: 0;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card:hover::before {
            opacity: 1;
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-light);
        }

        .card-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            letter-spacing: -0.25px;
        }

        .card-title .icon {
            margin-right: 12px;
            font-size: 20px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .menu-toggle {
                display: block;
            }
            
            .content-area {
                padding: 15px;
            }
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--text-primary);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }

        /* 快速操作按钮 */
        .quick-action-btn {
            display: flex;
            align-items: center;
            padding: 15px;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            text-align: left;
            width: 100%;
        }

        .quick-action-btn:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .quick-action-btn .icon {
            font-size: 24px;
            margin-right: 15px;
            color: var(--primary-color);
        }

        .quick-action-btn .title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 3px;
        }

        .quick-action-btn .desc {
            font-size: 13px;
            color: var(--text-secondary);
        }

        /* 现代化表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
            letter-spacing: 0.25px;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            background: var(--bg-secondary);
            transition: var(--transition);
            font-family: inherit;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            transform: translateY(-1px);
        }

        .form-input:hover, .form-select:hover {
            border-color: var(--border-dark);
        }

        /* 范围滑块样式 */
        .form-input[type="range"] {
            padding: 0;
            height: 6px;
            background: var(--border-color);
            border: none;
            border-radius: 3px;
            appearance: none;
        }

        .form-input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--gradient-primary);
            cursor: pointer;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .form-input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        .form-input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--gradient-primary);
            cursor: pointer;
            border: none;
            box-shadow: var(--shadow);
        }

        /* 现代化按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            user-select: none;
            letter-spacing: 0.25px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-inverse);
            box-shadow: var(--shadow);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-success {
            background: var(--gradient-accent);
            color: var(--text-inverse);
            box-shadow: var(--shadow);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-warning {
            background: var(--gradient-warm);
            color: var(--text-inverse);
            box-shadow: var(--shadow);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-error {
            background: linear-gradient(135deg, var(--error-color), var(--error-light));
            color: var(--text-inverse);
            box-shadow: var(--shadow);
        }

        .btn-error:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* 按钮尺寸变体 */
        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
        }

        .btn-lg {
            padding: 16px 32px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <!-- 现代化页面加载器 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h3>剪映自动混剪工具</h3>
            <p>正在加载现代化界面...</p>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div class="app-container">
        <!-- 侧边栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1>🎬 剪映混剪工具</h1>
                <div class="version">v2.1.0 - 现代化界面版</div>
            </div>
            
            <div class="sidebar-nav">
                <!-- 一级菜单：概览 -->
                <div class="nav-item">
                    <a class="nav-link active" data-section="overview">
                        <span class="icon">🏠</span>
                        <span class="text">概览</span>
                    </a>
                </div>

                <!-- 一级菜单：配置管理 -->
                <div class="nav-item">
                    <a class="nav-link" data-section="config" data-has-submenu="true">
                        <span class="icon">⚙️</span>
                        <span class="text">配置管理</span>
                        <span class="arrow">▶</span>
                    </a>
                    <div class="sub-nav">
                        <a class="sub-nav-link" data-section="config-basic">基础配置</a>
                        <a class="sub-nav-link" data-section="config-anti-detect">防审核设置</a>
                        <a class="sub-nav-link" data-section="config-effects">特效参数</a>
                        <a class="sub-nav-link" data-section="config-advanced">高级选项</a>
                    </div>
                </div>

                <!-- 一级菜单：素材管理 -->
                <div class="nav-item">
                    <a class="nav-link" data-section="materials" data-has-submenu="true">
                        <span class="icon">🎨</span>
                        <span class="text">素材管理</span>
                        <span class="arrow">▶</span>
                    </a>
                    <div class="sub-nav">
                        <a class="sub-nav-link" data-section="video-effects">视频特效</a>
                        <a class="sub-nav-link" data-section="filters">滤镜管理</a>
                        <a class="sub-nav-link" data-section="transitions">转场管理</a>
                        <a class="sub-nav-link" data-section="smart-exclusion">智能排除</a>
                    </div>
                </div>

                <!-- 一级菜单：混剪操作 -->
                <div class="nav-item">
                    <a class="nav-link" data-section="automix" data-has-submenu="true">
                        <span class="icon">🎬</span>
                        <span class="text">混剪操作</span>
                        <span class="arrow">▶</span>
                    </a>
                    <div class="sub-nav">
                        <a class="sub-nav-link" data-section="automix-unified">开始混剪</a>
                        <a class="sub-nav-link" data-section="automix-products">产品管理</a>
                    </div>
                </div>

                <!-- 一级菜单：帮助 -->
                <div class="nav-item">
                    <a class="nav-link" data-section="help">
                        <span class="icon">❓</span>
                        <span class="text">帮助文档</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content" id="mainContent">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="breadcrumb" id="breadcrumb">
                    <span class="breadcrumb-item">🏠 概览</span>
                </div>
                <div class="top-actions">
                    <button class="menu-toggle" id="menuToggle">☰</button>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area" id="contentArea">
                <!-- 概览页面 -->
                <section class="content-section active" id="section-overview">
                    <!-- 欢迎卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🎬</span>
                                欢迎使用剪映自动混剪工具 v2.1.0
                            </h2>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                            <div>
                                <h3 style="color: var(--primary-color); margin-bottom: 10px;">🎯 核心功能</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 5px 0;"><span style="color: var(--accent-color);">✓</span> 智能视频混剪</li>
                                    <li style="padding: 5px 0;"><span style="color: var(--accent-color);">✓</span> 轻微特效优化</li>
                                    <li style="padding: 5px 0;"><span style="color: var(--accent-color);">✓</span> 防审核技术</li>
                                    <li style="padding: 5px 0;"><span style="color: var(--accent-color);">✓</span> 批量生成</li>
                                </ul>
                            </div>
                            <div>
                                <h3 style="color: var(--primary-color); margin-bottom: 10px;">🆕 v2.1.0 更新</h3>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="padding: 5px 0;"><span style="color: var(--warning-color);">⭐</span> 纹理参数支持</li>
                                    <li style="padding: 5px 0;"><span style="color: var(--warning-color);">⭐</span> 滤镜参数支持</li>
                                    <li style="padding: 5px 0;"><span style="color: var(--warning-color);">⭐</span> 泰语字幕修复</li>
                                    <li style="padding: 5px 0;"><span style="color: var(--warning-color);">⭐</span> 界面性能优化</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <div class="card" style="text-align: center; padding: 15px;">
                            <div style="font-size: 24px; color: var(--primary-color); margin-bottom: 5px;">912</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">视频特效</div>
                        </div>
                        <div class="card" style="text-align: center; padding: 15px;">
                            <div style="font-size: 24px; color: var(--accent-color); margin-bottom: 5px;">468</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">滤镜效果</div>
                        </div>
                        <div class="card" style="text-align: center; padding: 15px;">
                            <div style="font-size: 24px; color: var(--warning-color); margin-bottom: 5px;">362</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">转场动画</div>
                        </div>
                        <div class="card" style="text-align: center; padding: 15px;">
                            <div style="font-size: 24px; color: var(--secondary-color); margin-bottom: 5px;">12</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">参数类型</div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">⚡</span>
                                快速操作
                            </h2>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                            <button class="quick-action-btn" data-action="config-basic">
                                <span class="icon">⚙️</span>
                                <div>
                                    <div class="title">基础配置</div>
                                    <div class="desc">设置素材路径和输出目录</div>
                                </div>
                            </button>
                            <button class="quick-action-btn" data-action="effects-smart">
                                <span class="icon">🎨</span>
                                <div>
                                    <div class="title">智能排除</div>
                                    <div class="desc">一键排除夸张特效</div>
                                </div>
                            </button>
                            <button class="quick-action-btn" data-action="automix-unified">
                                <span class="icon">🎬</span>
                                <div>
                                    <div class="title">开始混剪</div>
                                    <div class="desc">智能混剪，支持单个或批量生成</div>
                                </div>
                            </button>
                            <button class="quick-action-btn" data-action="automix-products">
                                <span class="icon">📦</span>
                                <div>
                                    <div class="title">产品管理</div>
                                    <div class="desc">管理素材库中的产品</div>
                                </div>
                            </button>
                        </div>
                    </div>
                </section>

                <!-- 其他内容区域将通过JavaScript动态加载 -->
            </div>
        </main>
    </div>

    <script>
        // 优化版应用类 - 性能重构
        class AutoMixApp {
            constructor() {
                this.currentSection = 'overview';
                this.sectionCache = new Map(); // 页面内容缓存
                this.domCache = new Map(); // DOM元素缓存
                this.systemMonitorInterval = null;
                this.statusPollInterval = null;
                this.init();
            }

            init() {
                this.bindEvents();
                this.hideLoader();
                this.initPerformanceOptimizations();
            }

            // 性能优化初始化
            initPerformanceOptimizations() {
                // 预缓存常用DOM元素
                this.cacheCommonElements();

                // 使用防抖处理搜索
                this.debouncedSearch = this.debounce(this.performSearch.bind(this), 300);

                // 优化轮询频率
                this.optimizePollingIntervals();
            }

            // 缓存常用DOM元素
            cacheCommonElements() {
                const commonElements = [
                    'sidebar', 'mainContent', 'contentArea', 'breadcrumb',
                    'menuToggle', 'pageLoader'
                ];

                commonElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        this.domCache.set(id, element);
                    }
                });
            }

            // 获取缓存的DOM元素
            getCachedElement(id) {
                if (this.domCache.has(id)) {
                    return this.domCache.get(id);
                }
                const element = document.getElementById(id);
                if (element) {
                    this.domCache.set(id, element);
                }
                return element;
            }

            // 防抖函数
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // 优化轮询间隔
            optimizePollingIntervals() {
                // 系统监控降低到10秒一次
                this.systemMonitorDelay = 10000;
                // 混剪状态降低到3秒一次
                this.statusPollDelay = 3000;
            }

            bindEvents() {
                // 使用事件委托优化性能
                document.addEventListener('click', this.handleGlobalClick.bind(this));

                // 移动端菜单切换
                const menuToggle = this.getCachedElement('menuToggle');
                if (menuToggle) {
                    menuToggle.addEventListener('click', () => {
                        this.toggleSidebar();
                    });
                }

                // 添加键盘快捷键支持
                document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

                // 窗口大小变化时优化响应
                window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
            }

            // 全局点击事件处理（事件委托）
            handleGlobalClick(e) {
                const navLink = e.target.closest('.nav-link');
                const subNavLink = e.target.closest('.sub-nav-link');
                const quickActionBtn = e.target.closest('.quick-action-btn');

                if (navLink) {
                    e.preventDefault();
                    this.handleNavClick(navLink);
                } else if (subNavLink) {
                    e.preventDefault();
                    this.handleSubNavClick(subNavLink);
                } else if (quickActionBtn) {
                    e.preventDefault();
                    this.handleQuickAction(quickActionBtn);
                }
            }

            // 键盘快捷键处理
            handleKeyboardShortcuts(e) {
                // Ctrl+1-5 快速切换主菜单
                if (e.ctrlKey && e.key >= '1' && e.key <= '5') {
                    e.preventDefault();
                    const sections = ['overview', 'config-basic', 'smart-exclusion', 'automix-unified', 'help'];
                    const sectionIndex = parseInt(e.key) - 1;
                    if (sections[sectionIndex]) {
                        this.switchSection(sections[sectionIndex]);
                    }
                }

                // ESC 关闭侧边栏（移动端）
                if (e.key === 'Escape') {
                    const sidebar = this.getCachedElement('sidebar');
                    if (sidebar && sidebar.classList.contains('mobile-open')) {
                        this.toggleSidebar();
                    }
                }
            }

            // 窗口大小变化处理
            handleResize() {
                // 在大屏幕上自动关闭移动端菜单
                if (window.innerWidth > 768) {
                    const sidebar = this.getCachedElement('sidebar');
                    if (sidebar) {
                        sidebar.classList.remove('mobile-open');
                    }
                }
            }

            handleNavClick(navLink) {
                const section = navLink.dataset.section;
                const hasSubmenu = navLink.dataset.hasSubmenu === 'true';

                if (hasSubmenu) {
                    this.toggleSubmenu(navLink);
                } else {
                    this.switchSection(section);
                    this.setActiveNav(navLink);
                }
            }

            handleSubNavClick(subNavLink) {
                const section = subNavLink.dataset.section;
                this.switchSection(section);
                this.setActiveSubNav(subNavLink);
            }

            handleQuickAction(actionBtn) {
                const action = actionBtn.dataset.action;
                this.switchSection(action);

                // 更新导航状态
                const targetSubNav = document.querySelector(`[data-section="${action}"]`);
                if (targetSubNav && targetSubNav.classList.contains('sub-nav-link')) {
                    this.setActiveSubNav(targetSubNav);
                    // 展开对应的父菜单
                    const parentNav = targetSubNav.closest('.nav-item').querySelector('.nav-link');
                    if (parentNav) {
                        this.toggleSubmenu(parentNav);
                    }
                }
            }

            toggleSubmenu(navLink) {
                const subNav = navLink.parentElement.querySelector('.sub-nav');
                const isExpanded = navLink.classList.contains('expanded');

                // 关闭其他展开的菜单
                document.querySelectorAll('.nav-link.expanded').forEach(link => {
                    if (link !== navLink) {
                        link.classList.remove('expanded');
                        link.parentElement.querySelector('.sub-nav').classList.remove('expanded');
                    }
                });

                if (isExpanded) {
                    navLink.classList.remove('expanded');
                    subNav.classList.remove('expanded');
                } else {
                    navLink.classList.add('expanded');
                    subNav.classList.add('expanded');
                }
            }

            switchSection(section) {
                // 防止重复切换到同一页面
                if (this.currentSection === section) {
                    return;
                }

                // 清理当前页面的定时器和事件监听器
                this.cleanupCurrentSection();

                // 使用缓存的DOM查询
                const contentSections = document.querySelectorAll('.content-section');
                contentSections.forEach(sec => {
                    sec.classList.remove('active');
                });

                // 显示目标区域（懒加载）
                let targetSection = document.getElementById(`section-${section}`);
                if (!targetSection) {
                    // 如果区域不存在，动态创建
                    targetSection = this.createSection(section);
                }

                // 使用requestAnimationFrame优化动画性能
                requestAnimationFrame(() => {
                    targetSection.classList.add('active');
                });

                this.currentSection = section;
                this.updateBreadcrumb(section);
            }

            // 清理当前页面的资源
            cleanupCurrentSection() {
                // 停止系统监控（如果不在智能排除页面）
                if (this.currentSection !== 'smart-exclusion') {
                    this.stopSystemMonitoring();
                }

                // 清理状态轮询
                if (this.statusPollInterval) {
                    clearInterval(this.statusPollInterval);
                    this.statusPollInterval = null;
                }
            }

            createSection(section) {
                // 检查缓存
                if (this.sectionCache.has(section)) {
                    const cachedContent = this.sectionCache.get(section);
                    const contentArea = this.getCachedElement('contentArea');
                    const sectionElement = document.createElement('section');
                    sectionElement.className = 'content-section';
                    sectionElement.id = `section-${section}`;
                    sectionElement.innerHTML = cachedContent;
                    contentArea.appendChild(sectionElement);

                    // 异步加载数据
                    this.loadSectionData(section);
                    return sectionElement;
                }

                // 生成新内容
                const contentArea = this.getCachedElement('contentArea');
                const sectionElement = document.createElement('section');
                sectionElement.className = 'content-section';
                sectionElement.id = `section-${section}`;

                const content = this.getSectionContent(section);
                sectionElement.innerHTML = content;

                // 缓存内容
                this.sectionCache.set(section, content);

                contentArea.appendChild(sectionElement);

                // 异步加载数据
                this.loadSectionData(section);

                return sectionElement;
            }

            // 异步加载页面数据
            async loadSectionData(section) {
                try {
                    // 如果是配置相关页面，加载配置数据
                    if (section.startsWith('config-')) {
                        await this.loadConfigData(section);
                    }

                    // 如果是混剪相关页面，加载产品列表
                    if (section.startsWith('automix-')) {
                        await this.loadProductsForAutomix(section);
                        // 如果是统一混剪页面，初始化模式
                        if (section === 'automix-unified') {
                            setTimeout(() => this.updateMixMode(), 100);
                        }
                    }

                    // 如果是滤镜管理页面，加载滤镜统计
                    if (section === 'filters') {
                        await this.refreshFilterStats();
                    }

                    // 如果是转场管理页面，加载转场统计
                    if (section === 'transitions') {
                        await this.refreshTransitionStats();
                    }

                    // 如果是智能排除页面，启动系统监控并加载特效管理功能
                    if (section === 'smart-exclusion') {
                        this.startSystemMonitoring();
                        await this.loadExclusionStats();
                        // 延迟执行搜索以避免阻塞UI
                        setTimeout(() => this.searchEffects(), 200);
                    }
                } catch (error) {
                    console.error(`加载页面数据失败 (${section}):`, error);
                    this.showAlert('页面数据加载失败，请刷新重试', 'error');
                }
            }

            getSectionTitle(section) {
                const titles = {
                    'overview': '概览',
                    'config-basic': '基础配置',
                    'config-anti-detect': '防审核设置',
                    'config-effects': '特效参数',
                    'config-advanced': '高级选项',
                    'video-effects': '视频特效参数',
                    'filters': '滤镜参数设置',
                    'transitions': '转场参数设置',
                    'smart-exclusion': '智能排除系统',
                    'automix-unified': '智能混剪',
                    'automix-products': '产品管理',
                    'help': '帮助文档'
                };
                return titles[section] || section;
            }

            getSectionContent(section) {
                switch(section) {
                    case 'config-basic':
                        return this.getBasicConfigContent();
                    case 'config-anti-detect':
                        return this.getAntiDetectConfigContent();
                    case 'config-effects':
                        return this.getEffectsConfigContent();
                    case 'config-advanced':
                        return this.getAdvancedConfigContent();
                    case 'video-effects':
                        return this.getVideoEffectsContent();
                    case 'filters':
                        return this.getFiltersContent();
                    case 'transitions':
                        return this.getTransitionsContent();
                    case 'smart-exclusion':
                        return this.getSmartExclusionContent();
                    case 'automix-unified':
                        return this.getUnifiedAutomixContent();
                    case 'automix-products':
                        return this.getAutomixProductsContent();
                    case 'help':
                        return this.getHelpContent();
                    default:
                        return `
                            <div class="card">
                                <div class="card-header">
                                    <h2 class="card-title">
                                        <span class="icon">⚙️</span>
                                        ${this.getSectionTitle(section)}
                                    </h2>
                                </div>
                                <p>正在开发中，敬请期待...</p>
                            </div>
                        `;
                }
            }

            setActiveNav(navLink) {
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                navLink.classList.add('active');
            }

            setActiveSubNav(subNavLink) {
                document.querySelectorAll('.sub-nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                subNavLink.classList.add('active');
            }

            updateBreadcrumb(section) {
                const breadcrumb = document.getElementById('breadcrumb');
                const title = this.getSectionTitle(section);
                breadcrumb.innerHTML = `<span class="breadcrumb-item">🏠 ${title}</span>`;
            }

            toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');
                
                sidebar.classList.toggle('mobile-open');
            }

            hideLoader() {
                try {
                    console.log('开始隐藏加载器...');
                    setTimeout(() => {
                        try {
                            const loader = this.getCachedElement('pageLoader') || document.getElementById('pageLoader');
                            console.log('找到加载器元素:', loader);
                            if (loader) {
                                loader.classList.add('hidden');
                                console.log('加载器已隐藏');

                                // 确保主内容可见
                                const appContainer = document.querySelector('.app-container');
                                if (appContainer) {
                                    appContainer.style.display = 'flex';
                                    console.log('主应用容器已显示');
                                }
                            } else {
                                console.error('未找到页面加载器元素');
                            }
                        } catch (error) {
                            console.error('隐藏加载器时出错:', error);
                            // 强制隐藏加载器
                            const loader = document.getElementById('pageLoader');
                            if (loader) {
                                loader.style.display = 'none';
                            }
                        }
                    }, 500); // 减少延迟时间
                } catch (error) {
                    console.error('hideLoader方法出错:', error);
                }
            }

            // 性能优化的搜索方法
            performSearch() {
                const currentSection = this.currentSection;
                if (currentSection === 'smart-exclusion') {
                    this.searchEffects();
                } else if (currentSection === 'filters') {
                    this.searchFilters();
                } else if (currentSection === 'transitions') {
                    this.searchTransitions();
                }
            }

            // 批量DOM更新优化
            batchDOMUpdate(updates) {
                // 使用DocumentFragment减少重排
                const fragment = document.createDocumentFragment();

                updates.forEach(update => {
                    if (update.type === 'text') {
                        const element = this.getCachedElement(update.id);
                        if (element) {
                            element.textContent = update.value;
                        }
                    } else if (update.type === 'html') {
                        const element = this.getCachedElement(update.id);
                        if (element) {
                            element.innerHTML = update.value;
                        }
                    }
                });
            }

            // 优化的元素查找和设置
            setElementText(id, text) {
                const element = this.getCachedElement(id);
                if (element && element.textContent !== text) {
                    element.textContent = text;
                }
            }

            setElementHTML(id, html) {
                const element = this.getCachedElement(id);
                if (element && element.innerHTML !== html) {
                    element.innerHTML = html;
                }
            }

            getInputValue(id) {
                const element = this.getCachedElement(id);
                return element ? element.value : '';
            }

            setInputValue(id, value) {
                const element = this.getCachedElement(id);
                if (element && element.value !== value) {
                    element.value = value;
                }
            }

            // 清理资源
            cleanup() {
                // 清理所有定时器
                this.stopSystemMonitoring();
                if (this.statusPollInterval) {
                    clearInterval(this.statusPollInterval);
                }

                // 清理缓存
                this.sectionCache.clear();
                this.domCache.clear();
            }

            // 配置页面内容生成方法
            getBasicConfigContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">📁</span>
                                基础配置
                            </h2>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">📁 素材库路径:</label>
                                <input type="text" class="form-input" id="material_path" placeholder="选择素材库路径">
                            </div>
                            <div class="form-group">
                                <label class="form-label">💾 草稿输出路径:</label>
                                <input type="text" class="form-input" id="draft_output_path" placeholder="选择输出路径">
                            </div>
                            <div class="form-group">
                                <label class="form-label">⏱️ 最小时长(秒):</label>
                                <input type="number" class="form-input" id="video_duration_min" min="5" max="300" value="30">
                            </div>
                            <div class="form-group">
                                <label class="form-label">⏱️ 最大时长(秒):</label>
                                <input type="number" class="form-input" id="video_duration_max" min="5" max="300" value="40">
                            </div>
                            <div class="form-group">
                                <label class="form-label">🎬 视频缩放(%):</label>
                                <input type="number" class="form-input" id="video_scale_factor" min="100" max="120" step="1" value="105">
                            </div>
                            <div class="form-group">
                                <label class="form-label">🔊 旁白音量(%):</label>
                                <input type="number" class="form-input" id="narration_volume" min="0" max="100" step="5" value="100">
                            </div>
                            <div class="form-group">
                                <label class="form-label">🎵 背景音量(%):</label>
                                <input type="number" class="form-input" id="background_volume" min="0" max="50" step="1" value="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">📦 批量生成数量:</label>
                                <input type="number" class="form-input" id="batch_count" min="1" max="100" value="5">
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="app.loadBasicConfig()">🔄 刷新配置</button>
                            <button class="btn btn-success" onclick="app.saveBasicConfig()">💾 保存配置</button>
                        </div>
                    </div>
                `;
            }

            getAntiDetectConfigContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🛡️</span>
                                高级防审核技术
                            </h2>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">🔄 镜像翻转概率:</label>
                                <input type="range" class="form-input" id="flip_probability" min="0" max="1" step="0.1" value="1.0"
                                       oninput="document.getElementById('flip_probability_value').textContent = Math.round(this.value * 100) + '%'">
                                <span id="flip_probability_value" style="color: var(--accent-color); font-weight: bold;">100%</span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">🌫️ 模糊背景:</label>
                                <select class="form-select" id="blur_background_enabled">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">🌫️ 模糊背景概率:</label>
                                <input type="range" class="form-input" id="blur_background_probability" min="0" max="1" step="0.1" value="1.0"
                                       oninput="document.getElementById('blur_background_probability_value').textContent = Math.round(this.value * 100) + '%'">
                                <span id="blur_background_probability_value" style="color: var(--accent-color); font-weight: bold;">100%</span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">📐 前景缩放(%):</label>
                                <input type="number" class="form-input" id="foreground_scale" min="50" max="100" step="5" value="80">
                            </div>
                            <div class="form-group">
                                <label class="form-label">📏 背景放大(%):</label>
                                <input type="number" class="form-input" id="background_scale" min="100" max="200" step="10" value="120">
                            </div>
                            <div class="form-group">
                                <label class="form-label">🌀 模糊强度(%):</label>
                                <input type="number" class="form-input" id="background_blur_intensity" min="10" max="100" step="10" value="50">
                            </div>
                            <div class="form-group">
                                <label class="form-label">🎞️ 抽帧处理:</label>
                                <select class="form-select" id="frame_manipulation_enabled">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">🎞️ 抽帧概率:</label>
                                <input type="range" class="form-input" id="frame_drop_probability" min="0" max="1" step="0.1" value="1.0"
                                       oninput="document.getElementById('frame_drop_probability_value').textContent = Math.round(this.value * 100) + '%'">
                                <span id="frame_drop_probability_value" style="color: var(--accent-color); font-weight: bold;">100%</span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">⏱️ 抽帧间隔(秒):</label>
                                <input type="number" class="form-input" id="frame_drop_interval" min="1" max="30" step="1" value="5">
                            </div>
                            <div class="form-group">
                                <label class="form-label">🔢 最大抽帧数:</label>
                                <input type="number" class="form-input" id="max_frame_drops_per_segment" min="1" max="10" step="1" value="3">
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="app.loadAntiDetectConfig()">🔄 刷新配置</button>
                            <button class="btn btn-success" onclick="app.saveAntiDetectConfig()">💾 保存配置</button>
                            <button class="btn btn-warning" onclick="app.setForceExecutionMode()">🎯 设为强制执行模式</button>
                        </div>
                    </div>
                `;
            }

            getEffectsConfigContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🎨</span>
                                轻微特效参数配置 (v2.1.0)
                            </h2>
                        </div>
                        <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="color: var(--text-secondary); margin: 0;">
                                💡 所有特效参数均控制在轻微范围内，避免过度强烈的视觉效果
                            </p>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">💡 滤镜强度最小值(%):</label>
                                <input type="number" class="form-input" id="filter_intensity_min" min="5" max="50" step="1" value="10">
                            </div>
                            <div class="form-group">
                                <label class="form-label">💡 滤镜强度最大值(%):</label>
                                <input type="number" class="form-input" id="filter_intensity_max" min="5" max="50" step="1" value="25">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 20px;">
                            <div style="background: var(--bg-secondary); padding: 15px; border-radius: 8px; border-left: 3px solid var(--primary-color);">
                                <h4 style="color: var(--primary-color); margin-bottom: 10px;">基础参数范围</h4>
                                <div style="font-size: 13px; line-height: 1.6;">
                                    <div>🌟 亮度参数: <span style="color: var(--text-secondary);">15-35 (轻微调整)</span></div>
                                    <div>🎭 对比度参数: <span style="color: var(--text-secondary);">20-40 (轻微调整)</span></div>
                                    <div>🎨 饱和度参数: <span style="color: var(--text-secondary);">25-45 (轻微调整)</span></div>
                                    <div>📏 大小参数: <span style="color: var(--text-secondary);">10-30 (轻微缩放)</span></div>
                                </div>
                            </div>
                            <div style="background: var(--bg-secondary); padding: 15px; border-radius: 8px; border-left: 3px solid var(--accent-color);">
                                <h4 style="color: var(--accent-color); margin-bottom: 10px;">高级参数范围</h4>
                                <div style="font-size: 13px; line-height: 1.6;">
                                    <div>⚡ 速度参数: <span style="color: var(--text-secondary);">25-45 (轻微变速)</span></div>
                                    <div>💪 强度参数: <span style="color: var(--text-secondary);">10-25 (轻微强度)</span></div>
                                    <div>👻 透明度参数: <span style="color: var(--text-secondary);">20-40 (轻微透明)</span></div>
                                    <div>🌫️ 模糊参数: <span style="color: var(--text-secondary);">5-20 (轻微模糊)</span></div>
                                </div>
                            </div>
                            <div style="background: var(--bg-secondary); padding: 15px; border-radius: 8px; border-left: 3px solid var(--warning-color);">
                                <h4 style="color: var(--warning-color); margin-bottom: 10px;">新增参数范围 ⭐</h4>
                                <div style="font-size: 13px; line-height: 1.6;">
                                    <div>🔄 旋转参数: <span style="color: var(--text-secondary);">10-30 (轻微旋转)</span></div>
                                    <div>🧱 纹理参数: <span style="color: var(--accent-color);">15-35 (轻微纹理)</span></div>
                                    <div>🎭 滤镜参数: <span style="color: var(--accent-color);">20-40 (轻微滤镜)</span></div>
                                    <div>🎯 其他参数: <span style="color: var(--text-secondary);">中心25±8 (正态分布)</span></div>
                                </div>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="app.loadEffectsConfig()">🔄 刷新配置</button>
                            <button class="btn btn-success" onclick="app.saveEffectsConfig()">💾 保存配置</button>
                        </div>
                    </div>
                `;
            }

            getUnifiedAutomixContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🎬</span>
                                智能混剪生成
                            </h2>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-top: 8px;">
                                <span id="mode-indicator">🎯 单个混剪模式</span>
                            </div>
                        </div>

                        <!-- 基础设置 -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="form-group">
                                <label class="form-label">📁 选择产品:</label>
                                <select class="form-select" id="unified_product">
                                    <option value="">请选择产品...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">📦 生成数量:</label>
                                <input type="number" class="form-input" id="unified_count" min="1" max="100" value="1"
                                       onchange="app.updateMixMode()" oninput="app.updateMixMode()">
                            </div>
                            <div class="form-group" id="single-duration-group">
                                <label class="form-label">⏱️ 目标时长(秒):</label>
                                <input type="number" class="form-input" id="unified_duration" min="10" max="300" value="35">
                            </div>
                        </div>

                        <!-- 批量模式的时长范围设置 -->
                        <div id="batch-duration-group" style="display: none; margin-bottom: 20px;">
                            <div class="form-group">
                                <label class="form-label">⏱️ 时长范围(秒):</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="number" class="form-input" id="unified_min_duration" min="10" max="300" value="30" style="width: 120px;">
                                    <span style="color: var(--text-secondary);">至</span>
                                    <input type="number" class="form-input" id="unified_max_duration" min="10" max="300" value="40" style="width: 120px;">
                                    <span style="color: var(--text-tertiary); font-size: 13px;">每个视频的时长将在此范围内随机选择</span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn btn-primary" onclick="app.startUnifiedAutomix()" style="font-size: 16px; padding: 15px 30px;">
                                <span id="start-button-text">🎬 开始生成混剪视频</span>
                            </button>
                        </div>

                        <!-- 混剪统计面板 -->
                        <div id="automix-stats-panel" style="margin-top: 20px; display: none;">
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 15px;">📊 混剪统计信息</h4>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 15px;">
                                    <div style="text-align: center; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                                        <div style="font-size: 20px; color: var(--primary-color); margin-bottom: 3px;" id="stats-total-materials">0</div>
                                        <div style="font-size: 11px; color: var(--text-secondary);">总素材数</div>
                                    </div>
                                    <div style="text-align: center; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                                        <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 3px;" id="stats-selected-materials">0</div>
                                        <div style="font-size: 11px; color: var(--text-secondary);">选择素材</div>
                                    </div>
                                    <div style="text-align: center; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                                        <div style="font-size: 20px; color: var(--warning-color); margin-bottom: 3px;" id="stats-applied-filters">0</div>
                                        <div style="font-size: 11px; color: var(--text-secondary);">应用滤镜</div>
                                    </div>
                                    <div style="text-align: center; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                                        <div style="font-size: 20px; color: var(--error-color); margin-bottom: 3px;" id="stats-applied-effects">0</div>
                                        <div style="font-size: 11px; color: var(--text-secondary);">应用特效</div>
                                    </div>
                                    <div style="text-align: center; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                                        <div style="font-size: 20px; color: var(--primary-color); margin-bottom: 3px;" id="stats-applied-transitions">0</div>
                                        <div style="font-size: 11px; color: var(--text-secondary);">应用转场</div>
                                    </div>
                                    <div style="text-align: center; padding: 12px; background: var(--bg-primary); border-radius: 6px;">
                                        <div style="font-size: 20px; color: var(--accent-color); margin-bottom: 3px;" id="stats-audio-tracks">0</div>
                                        <div style="font-size: 11px; color: var(--text-secondary);">音频轨道</div>
                                    </div>
                                </div>

                                <!-- 覆盖率统计 -->
                                <div style="background: var(--bg-secondary); padding: 12px; border-radius: 6px; margin-bottom: 10px;">
                                    <h5 style="color: var(--text-primary); margin-bottom: 8px; font-size: 13px;">📈 覆盖率统计</h5>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-size: 12px;">
                                        <div>
                                            <span style="color: var(--text-secondary);">滤镜覆盖率:</span>
                                            <span id="stats-filter-coverage" style="color: var(--warning-color); font-weight: bold;">0%</span>
                                        </div>
                                        <div>
                                            <span style="color: var(--text-secondary);">特效覆盖率:</span>
                                            <span id="stats-effect-coverage" style="color: var(--error-color); font-weight: bold;">0%</span>
                                        </div>
                                        <div>
                                            <span style="color: var(--text-secondary);">转场覆盖率:</span>
                                            <span id="stats-transition-coverage" style="color: var(--primary-color); font-weight: bold;">0%</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 产品信息 -->
                                <div style="background: var(--bg-secondary); padding: 10px; border-radius: 6px; font-size: 12px;">
                                    <span style="color: var(--text-secondary);">🎯 产品型号:</span>
                                    <span id="stats-product-model" style="color: var(--primary-color); font-weight: bold;">未选择</span>
                                    <span style="margin-left: 15px; color: var(--text-secondary);">⏱️ 视频时长:</span>
                                    <span id="stats-video-duration" style="color: var(--accent-color); font-weight: bold;">0.0s</span>
                                </div>
                            </div>
                        </div>

                        <div id="unified-progress" style="margin-top: 20px; display: none;">
                            <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                    <div style="display: flex; align-items: center;">
                                        <div class="loader-spinner" style="width: 20px; height: 20px; margin-right: 10px;"></div>
                                        <span id="unified-progress-text">正在生成中...</span>
                                    </div>
                                    <span id="unified-progress-count" style="font-weight: bold; color: var(--primary-color);">1/1</span>
                                </div>
                                <div style="background: var(--border-color); height: 6px; border-radius: 3px; overflow: hidden;">
                                    <div id="unified-progress-bar" style="background: var(--primary-color); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }



            getAdvancedConfigContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🔧</span>
                                高级选项配置
                            </h2>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">🎭 使用VIP特效:</label>
                                <select class="form-select" id="use_vip_effects">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">🖼️ Pexels叠加:</label>
                                <select class="form-select" id="pexels_overlay_enabled">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">👻 叠加透明度(%):</label>
                                <input type="number" class="form-input" id="pexels_overlay_opacity" min="0" max="100" step="5" value="30">
                            </div>
                            <div class="form-group">
                                <label class="form-label">⏱️ 修剪开始时长(秒):</label>
                                <input type="number" class="form-input" id="trim_start_duration" min="0" max="10" step="0.5" value="0">
                            </div>
                        </div>
                        <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <h4 style="color: var(--primary-color); margin-bottom: 10px;">💡 高级选项说明</h4>
                            <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary);">
                                <li>VIP特效: 启用后可使用付费特效库</li>
                                <li>Pexels叠加: 添加免费素材叠加层</li>
                                <li>叠加透明度: 控制叠加层的可见度</li>
                                <li>修剪开始时长: 跳过视频开头部分</li>
                            </ul>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="app.loadAdvancedConfig()">🔄 刷新配置</button>
                            <button class="btn btn-success" onclick="app.saveAdvancedConfig()">💾 保存配置</button>
                        </div>
                    </div>
                `;
            }



            getSmartExclusionContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🧠</span>
                                智能排除系统
                            </h2>
                        </div>
                        <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="color: var(--text-secondary); margin: 0;">
                                💡 智能排除系统可以根据预设规则自动排除夸张、不合适的特效，保持视频的专业性和观感。
                            </p>
                        </div>

                        <!-- 系统监控面板 -->
                        <div style="margin-bottom: 20px;">
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 15px;">📊 系统监控面板</h4>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                                    <div style="text-align: center; padding: 15px; background: var(--bg-primary); border-radius: 8px;">
                                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
                                            <span id="system-status" style="font-size: 20px; margin-right: 8px;">🟢</span>
                                            <span id="system-status-text" style="font-size: 14px; color: var(--text-secondary);">系统正常</span>
                                        </div>
                                        <div style="font-size: 12px; color: var(--text-tertiary);">系统状态</div>
                                    </div>
                                    <div style="text-align: center; padding: 15px; background: var(--bg-primary); border-radius: 8px;">
                                        <div style="font-size: 24px; color: var(--warning-color); margin-bottom: 5px;" id="active-tasks">0</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">活跃任务数</div>
                                    </div>
                                    <div style="text-align: center; padding: 15px; background: var(--bg-primary); border-radius: 8px;">
                                        <div style="font-size: 24px; color: var(--accent-color); margin-bottom: 5px;" id="completed-tasks">0</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">今日完成任务</div>
                                        <div style="font-size: 10px; color: var(--text-tertiary); margin-top: 2px;" id="completed-tasks-change">+0</div>
                                    </div>
                                    <div style="text-align: center; padding: 15px; background: var(--bg-primary); border-radius: 8px;">
                                        <div style="font-size: 24px; color: var(--error-color); margin-bottom: 5px;" id="error-count">0</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">错误次数</div>
                                    </div>
                                </div>

                                <!-- 当前操作状态 -->
                                <div style="background: var(--bg-secondary); padding: 12px; border-radius: 6px; margin-bottom: 15px;">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="margin-right: 8px;">🔄</span>
                                            <span style="font-size: 14px; color: var(--text-primary);">当前操作:</span>
                                            <span id="current-operation-text" style="margin-left: 8px; color: var(--primary-color);">空闲中</span>
                                        </div>
                                    </div>

                                    <!-- 进度条 -->
                                    <div id="progress-section" style="margin-top: 10px; display: none;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                            <span style="font-size: 12px; color: var(--text-secondary);">进度</span>
                                            <span id="progress-text" style="font-size: 12px; color: var(--primary-color);">0%</span>
                                        </div>
                                        <div style="background: var(--border-color); height: 4px; border-radius: 2px; overflow: hidden;">
                                            <div id="monitor-progress-bar" style="background: var(--primary-color); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作日志 -->
                                <div>
                                    <h5 style="color: var(--text-primary); margin-bottom: 10px; font-size: 14px;">📝 操作日志</h5>
                                    <div id="operation-logs" style="max-height: 150px; overflow-y: auto; background: var(--bg-secondary); padding: 10px; border-radius: 6px; font-size: 13px;">
                                        <div style="text-align: center; color: var(--text-secondary); padding: 20px;">暂无操作日志</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎭 夸张特效排除</h4>
                                <div style="margin-bottom: 15px;">
                                    <button class="btn btn-warning" onclick="app.excludeExaggeratedEffects()" style="width: 100%;">
                                        ❌ 排除夸张特效
                                    </button>
                                </div>
                                <div style="font-size: 13px; color: var(--text-secondary);">
                                    自动排除过于夸张、闪烁、扭曲的视频特效，保持视频专业性。
                                </div>
                            </div>
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--accent-color); margin-bottom: 15px;">🌈 强烈滤镜排除</h4>
                                <div style="margin-bottom: 15px;">
                                    <button class="btn btn-warning" onclick="app.excludeStrongFilters()" style="width: 100%;">
                                        ❌ 排除强烈滤镜
                                    </button>
                                </div>
                                <div style="font-size: 13px; color: var(--text-secondary);">
                                    自动排除颜色过于浓烈、对比度过高的滤镜效果。
                                </div>
                            </div>
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--warning-color); margin-bottom: 15px;">⚡ 快速转场排除</h4>
                                <div style="margin-bottom: 15px;">
                                    <button class="btn btn-warning" onclick="app.excludeFastTransitions()" style="width: 100%;">
                                        ❌ 排除快速转场
                                    </button>
                                </div>
                                <div style="font-size: 13px; color: var(--text-secondary);">
                                    自动排除过于快速、突兀的转场动画，保持流畅观感。
                                </div>
                            </div>
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--error-color); margin-bottom: 15px;">🚫 全面智能排除</h4>
                                <div style="margin-bottom: 15px;">
                                    <button class="btn btn-error" onclick="app.smartExcludeAll()" style="width: 100%; background: var(--error-color);">
                                        🧠 一键智能排除
                                    </button>
                                </div>
                                <div style="font-size: 13px; color: var(--text-secondary);">
                                    综合排除所有不合适的特效、滤镜和转场。
                                </div>
                            </div>
                        </div>
                        <div style="margin-top: 20px;">
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 15px;">📊 排除统计</h4>
                                <div id="smart-exclusion-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                                    <div style="text-align: center;">
                                        <div style="font-size: 18px; color: var(--primary-color);" id="smart-video-excluded">0</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">视频特效已排除</div>
                                        <div style="font-size: 10px; color: var(--text-tertiary); margin-top: 2px;" id="smart-video-change">+0</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-size: 18px; color: var(--accent-color);" id="smart-filters-excluded">0</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">滤镜已排除</div>
                                        <div style="font-size: 10px; color: var(--text-tertiary); margin-top: 2px;" id="smart-filters-change">+0</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-size: 18px; color: var(--warning-color);" id="smart-transitions-excluded">0</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">转场已排除</div>
                                        <div style="font-size: 10px; color: var(--text-tertiary); margin-top: 2px;" id="smart-transitions-change">+0</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="font-size: 18px; color: var(--error-color);" id="smart-total-excluded">0</div>
                                        <div style="font-size: 12px; color: var(--text-secondary);">总计已排除</div>
                                        <div style="font-size: 10px; color: var(--text-tertiary); margin-top: 2px;" id="smart-total-change">+0</div>
                                    </div>
                                </div>
                                <div style="text-align: center; margin-top: 15px;">
                                    <button class="btn btn-primary" onclick="app.refreshSmartStats()">🔄 刷新统计</button>
                                    <button class="btn btn-success" onclick="app.resetAllExclusions()">🔄 重置所有排除</button>
                                </div>
                            </div>
                        </div>

                        <!-- 详细特效管理 -->
                        <div style="margin-top: 20px;">
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 15px;">🔍 详细特效管理</h4>
                                <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                    <p style="color: var(--text-secondary); margin: 0; font-size: 14px;">
                                        💡 在这里可以搜索和管理具体的特效、滤镜、转场。支持按名称搜索和批量操作。
                                    </p>
                                </div>

                                <!-- 特效统计 -->
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                                    <div style="text-align: center; padding: 15px; background: var(--bg-primary); border-radius: 8px;">
                                        <div style="font-size: 24px; color: var(--primary-color); margin-bottom: 5px;" id="video-effects-count">912</div>
                                        <div style="font-size: 14px; color: var(--text-secondary);">视频特效总数</div>
                                        <div style="font-size: 12px; color: var(--accent-color); margin-top: 5px;" id="video-effects-excluded">已排除: 0</div>
                                    </div>
                                    <div style="text-align: center; padding: 15px; background: var(--bg-primary); border-radius: 8px;">
                                        <div style="font-size: 24px; color: var(--accent-color); margin-bottom: 5px;" id="filters-count">468</div>
                                        <div style="font-size: 14px; color: var(--text-secondary);">滤镜效果总数</div>
                                        <div style="font-size: 12px; color: var(--accent-color); margin-top: 5px;" id="filters-excluded">已排除: 0</div>
                                    </div>
                                    <div style="text-align: center; padding: 15px; background: var(--bg-primary); border-radius: 8px;">
                                        <div style="font-size: 24px; color: var(--warning-color); margin-bottom: 5px;" id="transitions-count">362</div>
                                        <div style="font-size: 14px; color: var(--text-secondary);">转场动画总数</div>
                                        <div style="font-size: 12px; color: var(--accent-color); margin-top: 5px;" id="transitions-excluded">已排除: 0</div>
                                    </div>
                                </div>

                                <!-- 搜索控件 -->
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                    <div class="form-group">
                                        <label class="form-label">🔍 搜索特效名称:</label>
                                        <input type="text" class="form-input" id="effect-search" placeholder="输入特效名称进行搜索...">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">📂 特效类型:</label>
                                        <select class="form-select" id="effect-type">
                                            <option value="all">全部类型</option>
                                            <option value="video_effects">视频特效</option>
                                            <option value="filters">滤镜效果</option>
                                            <option value="transitions">转场动画</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div style="margin-bottom: 15px;">
                                    <button class="btn btn-primary" onclick="app.searchEffects()">🔍 搜索特效</button>
                                    <button class="btn btn-warning" onclick="app.excludeSelected()">❌ 排除选中</button>
                                    <button class="btn btn-success" onclick="app.includeSelected()">✅ 包含选中</button>
                                    <button class="btn btn-primary" onclick="app.loadExclusionStats()">📊 刷新统计</button>
                                </div>

                                <!-- 特效列表 -->
                                <div id="effects-list" style="max-height: 400px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 8px; padding: 15px;">
                                    <p style="text-align: center; color: var(--text-secondary);">点击"搜索特效"开始管理特效排除</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }



            // 添加其他缺失的页面内容
            getVideoEffectsContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🎬</span>
                                视频特效参数设置
                            </h2>
                        </div>

                        <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="color: var(--text-secondary); margin: 0;">
                                💡 在这里设置视频特效的强度范围和应用概率。特效排除功能已整合到"智能排除"页面。
                            </p>
                        </div>

                        <!-- 视频特效强度控制 -->
                        <div class="card" style="padding: 15px; margin-bottom: 20px;">
                            <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎬 视频特效强度控制</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label class="form-label">最小强度 (%):</label>
                                    <input type="range" class="form-input" id="video-effect-min-intensity" min="10" max="80" value="20"
                                           oninput="document.getElementById('video-effect-min-value').textContent = this.value + '%'">
                                    <span id="video-effect-min-value" style="color: var(--primary-color); font-weight: bold;">20%</span>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大强度 (%):</label>
                                    <input type="range" class="form-input" id="video-effect-max-intensity" min="10" max="80" value="40"
                                           oninput="document.getElementById('video-effect-max-value').textContent = this.value + '%'">
                                    <span id="video-effect-max-value" style="color: var(--primary-color); font-weight: bold;">40%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 视频特效应用概率 -->
                        <div class="card" style="padding: 15px; margin-bottom: 20px;">
                            <h4 style="color: var(--accent-color); margin-bottom: 15px;">📊 视频特效应用概率</h4>
                            <div class="form-group">
                                <label class="form-label">特效应用概率 (%):</label>
                                <input type="range" class="form-input" id="video-effect-probability" min="0" max="100" value="60"
                                       oninput="document.getElementById('video-effect-prob-value').textContent = this.value + '%'">
                                <span id="video-effect-prob-value" style="color: var(--accent-color); font-weight: bold;">60%</span>
                                <div style="font-size: 12px; color: var(--text-secondary); margin-top: 5px;">
                                    设置为0%表示不使用视频特效，100%表示每个片段都使用特效
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="text-align: center; margin: 20px 0;">
                            <button class="btn btn-success" onclick="app.saveVideoEffectSettings()">💾 保存特效设置</button>
                            <button class="btn btn-primary" onclick="app.resetVideoEffectSettings()">🔄 重置为默认</button>
                        </div>
                    </div>
                `;
            }

            getFiltersContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🎭</span>
                                滤镜参数设置
                            </h2>
                        </div>

                        <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="color: var(--text-secondary); margin: 0;">
                                💡 在这里设置滤镜的强度范围和应用概率。特效排除功能已整合到"智能排除"页面。
                            </p>
                        </div>

                        <!-- 滤镜强度控制 -->
                        <div class="card" style="padding: 15px; margin-bottom: 20px;">
                            <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎨 滤镜强度控制</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label class="form-label">最小强度 (%):</label>
                                    <input type="range" class="form-input" id="filter-min-intensity" min="5" max="50" value="15"
                                           oninput="document.getElementById('filter-min-value').textContent = this.value + '%'">
                                    <span id="filter-min-value" style="color: var(--accent-color); font-weight: bold;">15%</span>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大强度 (%):</label>
                                    <input type="range" class="form-input" id="filter-max-intensity" min="5" max="50" value="25"
                                           oninput="document.getElementById('filter-max-value').textContent = this.value + '%'">
                                    <span id="filter-max-value" style="color: var(--accent-color); font-weight: bold;">25%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 滤镜应用概率 -->
                        <div class="card" style="padding: 15px; margin-bottom: 20px;">
                            <h4 style="color: var(--accent-color); margin-bottom: 15px;">📊 滤镜应用概率</h4>
                            <div class="form-group">
                                <label class="form-label">滤镜应用概率 (%):</label>
                                <input type="range" class="form-input" id="filter-probability" min="0" max="100" value="80"
                                       oninput="document.getElementById('filter-prob-value').textContent = this.value + '%'">
                                <span id="filter-prob-value" style="color: var(--accent-color); font-weight: bold;">80%</span>
                                <div style="font-size: 12px; color: var(--text-secondary); margin-top: 5px;">
                                    设置为0%表示不使用滤镜，100%表示每个片段都使用滤镜
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="text-align: center; margin: 20px 0;">
                            <button class="btn btn-success" onclick="app.saveFilterSettings()">💾 保存滤镜设置</button>
                            <button class="btn btn-primary" onclick="app.resetFilterSettings()">🔄 重置为默认</button>
                        </div>
                    </div>
                `;
            }

            getTransitionsContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">🔄</span>
                                转场参数设置
                            </h2>
                        </div>

                        <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p style="color: var(--text-secondary); margin: 0;">
                                💡 在这里设置转场的时长范围和应用概率。转场排除功能已整合到"智能排除"页面。
                            </p>
                        </div>

                        <!-- 转场时长控制 -->
                        <div class="card" style="padding: 15px; margin-bottom: 20px;">
                            <h4 style="color: var(--warning-color); margin-bottom: 15px;">⏱️ 转场时长控制</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label class="form-label">最小时长 (秒):</label>
                                    <input type="range" class="form-input" id="transition-min-duration" min="0.1" max="3.0" step="0.1" value="0.5"
                                           oninput="document.getElementById('transition-min-duration-value').textContent = this.value + 's'">
                                    <span id="transition-min-duration-value" style="color: var(--accent-color); font-weight: bold;">0.5s</span>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大时长 (秒):</label>
                                    <input type="range" class="form-input" id="transition-max-duration" min="0.1" max="3.0" step="0.1" value="2.0"
                                           oninput="document.getElementById('transition-max-duration-value').textContent = this.value + 's'">
                                    <span id="transition-max-duration-value" style="color: var(--accent-color); font-weight: bold;">2.0s</span>
                                </div>
                            </div>
                        </div>

                        <!-- 转场频率设置 -->
                        <div class="card" style="padding: 15px; margin-bottom: 20px;">
                            <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎯 转场频率设置</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label class="form-label">转场概率 (%):</label>
                                    <input type="range" class="form-input" id="transition-probability" min="0" max="100" value="80"
                                           oninput="document.getElementById('transition-probability-value').textContent = this.value + '%'">
                                    <span id="transition-probability-value" style="color: var(--accent-color); font-weight: bold;">80%</span>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大连续转场:</label>
                                    <input type="number" class="form-input" id="max-consecutive-transitions" min="1" max="10" value="3">
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="text-align: center; margin: 20px 0;">
                            <button class="btn btn-success" onclick="app.saveTransitionSettings()">💾 保存转场设置</button>
                            <button class="btn btn-primary" onclick="app.resetTransitionSettings()">🔄 重置为默认</button>
                        </div>
                    </div>
                `;
            }

            getAutomixProductsContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">📦</span>
                                产品选择
                            </h2>
                        </div>
                        <div id="products-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px;">
                            <div style="text-align: center; color: var(--text-secondary); padding: 20px;">
                                正在加载产品列表...
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="app.loadProducts()">🔄 刷新产品列表</button>
                        </div>
                    </div>
                `;
            }

            getHelpContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span class="icon">❓</span>
                                帮助文档
                            </h2>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--primary-color); margin-bottom: 15px;">🚀 快速开始</h4>
                                <ol style="margin: 0; padding-left: 20px; color: var(--text-secondary);">
                                    <li>配置素材库路径和输出路径</li>
                                    <li>设置视频时长和音量参数</li>
                                    <li>选择要排除的特效（可选）</li>
                                    <li>选择产品并开始混剪</li>
                                </ol>
                            </div>
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--accent-color); margin-bottom: 15px;">⚙️ 配置说明</h4>
                                <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary);">
                                    <li>基础配置：路径、时长、音量设置</li>
                                    <li>防审核：镜像、模糊、抽帧技术</li>
                                    <li>特效参数：轻微效果范围控制</li>
                                    <li>高级选项：VIP特效、叠加设置</li>
                                </ul>
                            </div>
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--warning-color); margin-bottom: 15px;">🎨 特效管理</h4>
                                <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary);">
                                    <li>特效排除：手动搜索排除特效</li>
                                    <li>智能排除：一键排除夸张特效</li>
                                    <li>滤镜管理：管理468种滤镜</li>
                                    <li>转场设置：管理362种转场</li>
                                </ul>
                            </div>
                            <div class="card" style="padding: 15px;">
                                <h4 style="color: var(--error-color); margin-bottom: 15px;">🔧 常见问题</h4>
                                <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary);">
                                    <li>素材路径必须包含视频文件</li>
                                    <li>输出路径需要有写入权限</li>
                                    <li>建议时长设置在30-60秒</li>
                                    <li>防审核技术可提高通过率</li>
                                </ul>
                            </div>
                        </div>
                        <div style="background: var(--bg-primary); padding: 15px; border-radius: 8px; margin-top: 20px;">
                            <h4 style="color: var(--primary-color); margin-bottom: 10px;">📞 技术支持</h4>
                            <p style="color: var(--text-secondary); margin: 0;">
                                如果遇到问题，请检查控制台输出或查看操作日志。确保所有路径设置正确，素材文件完整。
                            </p>
                        </div>
                    </div>
                `;
            }

            // 配置加载和保存方法
            async loadConfigData(section) {
                try {
                    const response = await fetch('/api/config');
                    const config = await response.json();

                    if (config.error) {
                        this.showAlert('加载配置失败: ' + config.error, 'error');
                        return;
                    }

                    this.populateConfigForm(section, config);
                } catch (error) {
                    this.showAlert('加载配置失败: ' + error.message, 'error');
                }
            }

            populateConfigForm(section, config) {
                if (section === 'config-basic') {
                    this.setInputValue('material_path', config.material_path || '');
                    this.setInputValue('draft_output_path', config.draft_output_path || '');
                    this.setInputValue('video_duration_min', config.video_duration_min || 30);
                    this.setInputValue('video_duration_max', config.video_duration_max || 40);
                    this.setInputValue('video_scale_factor', Math.round((config.video_scale_factor || 1.05) * 100));
                    this.setInputValue('narration_volume', Math.round((config.narration_volume || 1.0) * 100));
                    this.setInputValue('background_volume', Math.round((config.background_volume || 0.1) * 100));
                    this.setInputValue('batch_count', config.batch_count || 5);
                } else if (section === 'config-anti-detect') {
                    this.setInputValue('flip_probability', config.flip_probability || 1.0);
                    this.setInputValue('blur_background_enabled', config.blur_background_enabled ? 'true' : 'false');
                    this.setInputValue('blur_background_probability', config.blur_background_probability || 1.0);
                    this.setInputValue('foreground_scale', Math.round((config.foreground_scale || 0.8) * 100));
                    this.setInputValue('background_scale', Math.round((config.background_scale || 1.2) * 100));
                    this.setInputValue('background_blur_intensity', Math.round((config.background_blur_intensity || 0.5) * 100));
                    this.setInputValue('frame_manipulation_enabled', config.frame_manipulation_enabled ? 'true' : 'false');
                    this.setInputValue('frame_drop_probability', config.frame_drop_probability || 1.0);
                    this.setInputValue('frame_drop_interval', config.frame_drop_interval || 5);
                    this.setInputValue('max_frame_drops_per_segment', config.max_frame_drops_per_segment || 3);

                    // 更新滑块显示值
                    this.updateRangeValue('flip_probability', config.flip_probability || 1.0);
                    this.updateRangeValue('blur_background_probability', config.blur_background_probability || 1.0);
                    this.updateRangeValue('frame_drop_probability', config.frame_drop_probability || 1.0);
                } else if (section === 'config-effects') {
                    this.setInputValue('filter_intensity_min', config.filter_intensity_min || 10);
                    this.setInputValue('filter_intensity_max', config.filter_intensity_max || 25);
                }
            }

            setInputValue(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.value = value;
                }
            }

            updateRangeValue(id, value) {
                const valueElement = document.getElementById(id + '_value');
                if (valueElement) {
                    valueElement.textContent = Math.round(value * 100) + '%';
                }
            }

            async saveBasicConfig() {
                await this.saveConfig('basic');
            }

            async saveAntiDetectConfig() {
                await this.saveConfig('anti-detect');
            }

            async saveEffectsConfig() {
                await this.saveConfig('effects');
            }

            async saveConfig(type) {
                try {
                    const config = this.collectConfigData(type);

                    const response = await fetch('/api/config', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(config)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert('配置保存成功', 'success');
                    } else {
                        this.showAlert('配置保存失败: ' + result.errors.join(', '), 'error');
                    }
                } catch (error) {
                    this.showAlert('保存配置失败: ' + error.message, 'error');
                }
            }

            collectConfigData(type) {
                const config = {};

                if (type === 'basic') {
                    config.material_path = this.getInputValue('material_path');
                    config.draft_output_path = this.getInputValue('draft_output_path');
                    config.video_duration_min = parseInt(this.getInputValue('video_duration_min'));
                    config.video_duration_max = parseInt(this.getInputValue('video_duration_max'));
                    config.video_scale_factor = parseFloat(this.getInputValue('video_scale_factor')) / 100;
                    config.narration_volume = parseFloat(this.getInputValue('narration_volume')) / 100;
                    config.background_volume = parseFloat(this.getInputValue('background_volume')) / 100;
                    config.batch_count = parseInt(this.getInputValue('batch_count'));
                } else if (type === 'anti-detect') {
                    config.flip_probability = parseFloat(this.getInputValue('flip_probability'));
                    config.blur_background_enabled = this.getInputValue('blur_background_enabled') === 'true';
                    config.blur_background_probability = parseFloat(this.getInputValue('blur_background_probability'));
                    config.foreground_scale = parseFloat(this.getInputValue('foreground_scale')) / 100;
                    config.background_scale = parseFloat(this.getInputValue('background_scale')) / 100;
                    config.background_blur_intensity = parseFloat(this.getInputValue('background_blur_intensity')) / 100;
                    config.frame_manipulation_enabled = this.getInputValue('frame_manipulation_enabled') === 'true';
                    config.frame_drop_probability = parseFloat(this.getInputValue('frame_drop_probability'));
                    config.frame_drop_interval = parseFloat(this.getInputValue('frame_drop_interval'));
                    config.max_frame_drops_per_segment = parseInt(this.getInputValue('max_frame_drops_per_segment'));
                } else if (type === 'effects') {
                    config.filter_intensity_min = parseInt(this.getInputValue('filter_intensity_min'));
                    config.filter_intensity_max = parseInt(this.getInputValue('filter_intensity_max'));
                }

                return config;
            }

            getInputValue(id) {
                const element = document.getElementById(id);
                return element ? element.value : '';
            }

            showAlert(message, type) {
                // 简单的提示实现
                const alertDiv = document.createElement('div');
                alertDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 10000;
                    max-width: 300px;
                    box-shadow: var(--shadow);
                `;

                if (type === 'success') {
                    alertDiv.style.background = 'var(--accent-color)';
                } else if (type === 'error') {
                    alertDiv.style.background = 'var(--error-color)';
                } else {
                    alertDiv.style.background = 'var(--primary-color)';
                }

                alertDiv.textContent = message;
                document.body.appendChild(alertDiv);

                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
            }

            // 新增页面功能方法
            async loadAdvancedConfig() {
                await this.loadConfigData('config-advanced');
            }

            async saveAdvancedConfig() {
                await this.saveConfig('advanced');
            }

            async loadExclusionStats() {
                try {
                    const response = await fetch('/api/exclusions');
                    const stats = await response.json();

                    if (stats.error) {
                        this.showAlert('加载排除统计失败: ' + stats.error, 'error');
                        return;
                    }

                    // 更新统计显示
                    this.setElementText('video-effects-excluded', `已排除: ${stats.video_effects?.excluded || 0}`);
                    this.setElementText('filters-excluded', `已排除: ${stats.filters?.excluded || 0}`);
                    this.setElementText('transitions-excluded', `已排除: ${stats.transitions?.excluded || 0}`);
                } catch (error) {
                    this.showAlert('加载排除统计失败: ' + error.message, 'error');
                }
            }



            async loadProducts() {
                try {
                    const response = await fetch('/api/products');
                    const result = await response.json();

                    if (!result.success) {
                        this.showAlert('加载产品失败: ' + result.error, 'error');
                        return;
                    }

                    const productsList = document.getElementById('products-list');
                    if (productsList) {
                        if (result.products.length === 0) {
                            productsList.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">未找到可用产品</div>';
                        } else {
                            productsList.innerHTML = result.products.map(product => `
                                <div class="card" style="padding: 15px; text-align: center;">
                                    <div style="font-size: 18px; margin-bottom: 10px;">📦</div>
                                    <div style="font-weight: 600; margin-bottom: 5px;">${product.name}</div>
                                    <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 10px;">${product.path}</div>
                                    <button class="btn btn-primary" onclick="app.selectProduct('${product.name}')" style="width: 100%;">选择</button>
                                </div>
                            `).join('');
                        }
                    }
                } catch (error) {
                    this.showAlert('加载产品失败: ' + error.message, 'error');
                }
            }

            setElementText(id, text) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = text;
                }
            }

            // 智能排除功能
            async excludeExaggeratedEffects() {
                try {
                    this.showAlert('正在排除夸张特效...', 'info');

                    const response = await fetch('/api/effects/smart-exclude', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'exaggerated_effects'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert(`夸张特效排除完成，共排除 ${result.excluded_count} 个`, 'success');
                        this.refreshSmartStats();
                        this.loadExclusionStats();
                    } else {
                        this.showAlert('排除失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('排除失败: ' + error.message, 'error');
                }
            }

            async excludeStrongFilters() {
                try {
                    this.showAlert('正在排除强烈滤镜...', 'info');

                    const response = await fetch('/api/effects/smart-exclude', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'strong_filters'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert(`强烈滤镜排除完成，共排除 ${result.excluded_count} 个`, 'success');
                        this.refreshSmartStats();
                        this.loadExclusionStats();
                    } else {
                        this.showAlert('排除失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('排除失败: ' + error.message, 'error');
                }
            }

            async excludeFastTransitions() {
                try {
                    this.showAlert('正在排除快速转场...', 'info');

                    const response = await fetch('/api/effects/smart-exclude', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'fast_transitions'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert(`快速转场排除完成，共排除 ${result.excluded_count} 个`, 'success');
                        this.refreshSmartStats();
                        this.loadExclusionStats();
                    } else {
                        this.showAlert('排除失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('排除失败: ' + error.message, 'error');
                }
            }

            async smartExcludeAll() {
                try {
                    this.showAlert('正在执行全面智能排除...', 'info');

                    const response = await fetch('/api/effects/smart-exclude', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'all'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert(`全面智能排除完成，共排除 ${result.total_excluded} 个`, 'success');
                        this.refreshSmartStats();
                        this.loadExclusionStats();
                    } else {
                        this.showAlert('排除失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('排除失败: ' + error.message, 'error');
                }
            }

            async refreshSmartStats() {
                try {
                    const response = await fetch('/api/exclusions');
                    const stats = await response.json();

                    if (stats.success !== false) {
                        const videoExcluded = stats.video_effects?.excluded || 0;
                        const filtersExcluded = stats.filters?.excluded || 0;
                        const transitionsExcluded = stats.transitions?.excluded || 0;
                        const total = videoExcluded + filtersExcluded + transitionsExcluded;

                        // 更新智能排除统计（带变化显示）
                        this.updateSmartStatWithChange('smart-video-excluded', 'smart-video-change', videoExcluded);
                        this.updateSmartStatWithChange('smart-filters-excluded', 'smart-filters-change', filtersExcluded);
                        this.updateSmartStatWithChange('smart-transitions-excluded', 'smart-transitions-change', transitionsExcluded);
                        this.updateSmartStatWithChange('smart-total-excluded', 'smart-total-change', total);

                        // 更新详细特效管理统计
                        const videoTotal = stats.video_effects?.total || 912;
                        const filtersTotal = stats.filters?.total || 468;
                        const transitionsTotal = stats.transitions?.total || 362;

                        this.setElementText('video-effects-excluded', `已排除: ${videoExcluded}`);
                        this.setElementText('filters-excluded', `已排除: ${filtersExcluded}`);
                        this.setElementText('transitions-excluded', `已排除: ${transitionsExcluded}`);

                        // 显示可用数量和排除比例
                        const videoAvailable = videoTotal - videoExcluded;
                        const filtersAvailable = filtersTotal - filtersExcluded;
                        const transitionsAvailable = transitionsTotal - transitionsExcluded;

                        const videoExcludedPercent = ((videoExcluded / videoTotal) * 100).toFixed(1);
                        const filtersExcludedPercent = ((filtersExcluded / filtersTotal) * 100).toFixed(1);
                        const transitionsExcludedPercent = ((transitionsExcluded / transitionsTotal) * 100).toFixed(1);

                        // 更新详细统计显示
                        const videoExcludedElement = document.getElementById('video-effects-excluded');
                        const filtersExcludedElement = document.getElementById('filters-excluded');
                        const transitionsExcludedElement = document.getElementById('transitions-excluded');

                        if (videoExcludedElement) {
                            videoExcludedElement.innerHTML = `已排除: ${videoExcluded} (${videoExcludedPercent}%)`;
                        }
                        if (filtersExcludedElement) {
                            filtersExcludedElement.innerHTML = `已排除: ${filtersExcluded} (${filtersExcludedPercent}%)`;
                        }
                        if (transitionsExcludedElement) {
                            transitionsExcludedElement.innerHTML = `已排除: ${transitionsExcluded} (${transitionsExcludedPercent}%)`;
                        }

                        this.showAlert(`统计已更新: 总计排除 ${total} 个特效`, 'success');
                    }
                } catch (error) {
                    console.error('刷新智能排除统计失败:', error);
                    this.showAlert('刷新统计失败: ' + error.message, 'error');
                }
            }

            updateSmartStatWithChange(statElementId, changeElementId, newValue) {
                const statElement = document.getElementById(statElementId);
                const changeElement = document.getElementById(changeElementId);

                if (statElement) {
                    const oldValue = parseInt(statElement.textContent) || 0;
                    statElement.textContent = newValue;

                    if (changeElement) {
                        if (newValue > oldValue) {
                            const change = newValue - oldValue;
                            changeElement.textContent = `+${change}`;
                            changeElement.style.color = 'var(--warning-color)';
                            changeElement.style.fontWeight = 'bold';

                            // 添加闪烁效果
                            changeElement.style.animation = 'pulse 1.5s ease-in-out';
                            setTimeout(() => {
                                if (changeElement) {
                                    changeElement.style.animation = '';
                                    changeElement.style.fontWeight = 'normal';
                                    // 3秒后恢复为总计显示
                                    setTimeout(() => {
                                        if (changeElement) {
                                            changeElement.textContent = `总计: ${newValue}`;
                                            changeElement.style.color = 'var(--text-tertiary)';
                                        }
                                    }, 3000);
                                }
                            }, 1500);
                        } else if (newValue < oldValue) {
                            // 减少的情况（重置或移除排除）
                            const change = oldValue - newValue;
                            changeElement.textContent = `-${change}`;
                            changeElement.style.color = 'var(--accent-color)';
                            changeElement.style.fontWeight = 'bold';

                            setTimeout(() => {
                                if (changeElement) {
                                    changeElement.textContent = `总计: ${newValue}`;
                                    changeElement.style.color = 'var(--text-tertiary)';
                                    changeElement.style.fontWeight = 'normal';
                                }
                            }, 3000);
                        } else {
                            // 无变化
                            changeElement.textContent = `总计: ${newValue}`;
                            changeElement.style.color = 'var(--text-tertiary)';
                            changeElement.style.fontWeight = 'normal';
                        }
                    }
                }
            }

            setForceExecutionMode() {
                // 设置所有概率为100%
                this.setInputValue('flip_probability', 1.0);
                this.setInputValue('blur_background_probability', 1.0);
                this.setInputValue('frame_drop_probability', 1.0);

                // 更新显示
                this.updateRangeValue('flip_probability', 1.0);
                this.updateRangeValue('blur_background_probability', 1.0);
                this.updateRangeValue('frame_drop_probability', 1.0);

                this.showAlert('已设置为强制执行模式（100%执行）', 'success');
            }

            // 混剪操作功能
            async startSingleAutomix() {
                const selectedProduct = this.getInputValue('selected_product');
                const targetDuration = this.getInputValue('target_duration');

                if (!selectedProduct) {
                    this.showAlert('请先选择产品', 'error');
                    return;
                }

                if (!targetDuration || targetDuration < 10 || targetDuration > 300) {
                    this.showAlert('请设置有效的目标时长 (10-300秒)', 'error');
                    return;
                }

                try {
                    // 显示进度
                    this.showAutomixProgress();

                    // 调用后端API开始混剪
                    const response = await fetch('/api/automix/single', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            product: selectedProduct,
                            duration: parseInt(targetDuration)
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert('混剪任务已启动', 'success');
                        this.pollAutomixStatus('single');
                    } else {
                        this.hideAutomixProgress();
                        this.showAlert('启动混剪失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.hideAutomixProgress();
                    this.showAlert('启动混剪失败: ' + error.message, 'error');
                }
            }

            async startBatchAutomix() {
                const selectedProduct = this.getInputValue('batch_selected_product');
                const batchCount = this.getInputValue('batch_count_input');
                const minDuration = this.getInputValue('batch_min_duration');
                const maxDuration = this.getInputValue('batch_max_duration');

                if (!selectedProduct) {
                    this.showAlert('请先选择产品', 'error');
                    return;
                }

                if (!batchCount || batchCount < 1 || batchCount > 50) {
                    this.showAlert('请设置有效的生成数量 (1-50个)', 'error');
                    return;
                }

                if (!minDuration || !maxDuration || minDuration >= maxDuration) {
                    this.showAlert('请设置有效的时长范围', 'error');
                    return;
                }

                try {
                    // 显示批量进度
                    this.showBatchProgress();

                    // 调用后端API开始批量混剪
                    const response = await fetch('/api/automix/batch', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            product: selectedProduct,
                            count: parseInt(batchCount),
                            min_duration: parseInt(minDuration),
                            max_duration: parseInt(maxDuration)
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert('批量混剪任务已启动', 'success');
                        this.pollAutomixStatus('batch');
                    } else {
                        this.hideBatchProgress();
                        this.showAlert('启动批量混剪失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.hideBatchProgress();
                    this.showAlert('启动批量混剪失败: ' + error.message, 'error');
                }
            }

            showAutomixProgress() {
                const progressDiv = document.getElementById('automix-progress');
                if (progressDiv) {
                    progressDiv.style.display = 'block';
                    this.setElementText('progress-text', '正在初始化...');
                    this.updateProgressBar('progress-bar', 0);
                }
            }

            hideAutomixProgress() {
                const progressDiv = document.getElementById('automix-progress');
                if (progressDiv) {
                    progressDiv.style.display = 'none';
                }
            }

            showBatchProgress() {
                const progressDiv = document.getElementById('batch-progress');
                if (progressDiv) {
                    progressDiv.style.display = 'block';
                    this.setElementText('batch-progress-text', '正在初始化批量任务...');
                    this.setElementText('batch-progress-count', '0/' + this.getInputValue('batch_count_input'));
                    this.updateProgressBar('batch-progress-bar', 0);
                }
            }

            hideBatchProgress() {
                const progressDiv = document.getElementById('batch-progress');
                if (progressDiv) {
                    progressDiv.style.display = 'none';
                }
            }

            updateProgressBar(barId, percentage) {
                const progressBar = document.getElementById(barId);
                if (progressBar) {
                    progressBar.style.width = percentage + '%';
                }
            }

            async pollAutomixStatus(type) {
                // 防止重复轮询
                if (this.statusPollInterval) {
                    clearInterval(this.statusPollInterval);
                }

                this.statusPollInterval = setInterval(async () => {
                    // 只在页面可见且当前在相关页面时轮询
                    if (document.hidden ||
                        (!this.currentSection.includes('automix') && this.currentSection !== 'overview')) {
                        return;
                    }

                    try {
                        const response = await fetch('/api/status');
                        const status = await response.json();

                        if (type === 'single') {
                            this.setElementText('progress-text', status.progress || '处理中...');

                            // 解析进度百分比并更新进度条
                            const progressMatch = (status.progress || '').match(/\((\d+\.?\d*)%\)/);
                            if (progressMatch) {
                                const percentage = parseFloat(progressMatch[1]);
                                this.updateProgressBar('progress-bar', percentage);
                            }

                            if (!status.running) {
                                clearInterval(this.statusPollInterval);
                                this.statusPollInterval = null;

                                if (status.result) {
                                    // 显示详细的成功结果
                                    const result = status.result;
                                    const resultHtml = `
                                        <div style="text-align: center; color: var(--accent-color); padding: 20px;">
                                            <div style="font-size: 18px; margin-bottom: 15px;">✅ 混剪完成！</div>
                                            <div style="text-align: left; margin-bottom: 15px; background: var(--bg-primary); padding: 15px; border-radius: 8px;">
                                                <div><strong>草稿名称:</strong> ${result.draft_name || '未知'}</div>
                                                <div><strong>草稿路径:</strong> ${result.draft_path || '未知'}</div>
                                                <div><strong>视频时长:</strong> ${result.duration}秒</div>
                                                <div><strong>视频片段:</strong> ${result.video_count || 0}个</div>
                                                <div><strong>特效数量:</strong> ${result.effects_count || 0}个</div>
                                                <div><strong>转场数量:</strong> ${result.transitions_count || 0}个</div>
                                                <div><strong>滤镜数量:</strong> ${result.filters_count || 0}个</div>
                                            </div>
                                            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px;">
                                                草稿已保存到剪映项目目录，可在剪映中打开编辑
                                            </div>
                                            <button class="btn btn-primary" onclick="app.hideAutomixProgress()">确定</button>
                                        </div>
                                    `;

                                    const progressDiv = document.getElementById('automix-progress');
                                    if (progressDiv) {
                                        progressDiv.innerHTML = resultHtml;
                                    }
                                } else if (status.error) {
                                    this.hideAutomixProgress();
                                    this.showAlert('混剪失败: ' + status.error, 'error');
                                } else {
                                    this.hideAutomixProgress();
                                }
                            }
                        } else if (type === 'batch') {
                            // 更新批量进度文本
                            this.setElementText('batch-progress-text', status.progress || '批量处理中...');

                            // 更新进度计数和进度条
                            if (status.current_count !== undefined && status.total_count !== undefined) {
                                this.setElementText('batch-progress-count', `${status.current_count}/${status.total_count}`);
                                const percentage = status.total_count > 0 ? (status.current_count / status.total_count) * 100 : 0;
                                this.updateProgressBar('batch-progress-bar', percentage);
                            }

                            if (!status.running) {
                                clearInterval(this.statusPollInterval);
                                this.statusPollInterval = null;

                                if (status.result) {
                                    // 显示详细的批量结果
                                    const result = status.result;
                                    const successResults = result.results.filter(r => r.status === 'success');
                                    const failedResults = result.results.filter(r => r.status === 'failed');

                                    let resultHtml = `
                                        <div style="text-align: center; color: var(--accent-color); padding: 20px;">
                                            <div style="font-size: 18px; margin-bottom: 15px;">✅ 批量混剪完成！</div>
                                            <div style="text-align: left; margin-bottom: 15px; background: var(--bg-primary); padding: 15px; border-radius: 8px;">
                                                <div><strong>总数量:</strong> ${result.total_count}个</div>
                                                <div><strong>成功:</strong> ${result.successful_count}个</div>
                                                <div><strong>失败:</strong> ${result.failed_count}个</div>
                                                <div><strong>总时长:</strong> ${result.total_duration}秒</div>
                                            </div>
                                    `;

                                    if (successResults.length > 0) {
                                        resultHtml += `
                                            <div style="text-align: left; margin-bottom: 15px;">
                                                <h4 style="color: var(--accent-color); margin-bottom: 10px;">✅ 成功的草稿:</h4>
                                                <div style="max-height: 200px; overflow-y: auto; background: var(--bg-primary); padding: 10px; border-radius: 6px;">
                                        `;

                                        successResults.forEach(r => {
                                            resultHtml += `
                                                <div style="margin-bottom: 8px; padding: 8px; background: var(--bg-secondary); border-radius: 4px;">
                                                    <div><strong>${r.draft_name}</strong></div>
                                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                                        时长: ${r.duration}s | 片段: ${r.video_count || 0} | 特效: ${r.effects_count || 0} | 转场: ${r.transitions_count || 0}
                                                    </div>
                                                </div>
                                            `;
                                        });

                                        resultHtml += `</div></div>`;
                                    }

                                    if (failedResults.length > 0) {
                                        resultHtml += `
                                            <div style="text-align: left; margin-bottom: 15px;">
                                                <h4 style="color: var(--error-color); margin-bottom: 10px;">❌ 失败的任务:</h4>
                                                <div style="max-height: 150px; overflow-y: auto; background: var(--bg-primary); padding: 10px; border-radius: 6px;">
                                        `;

                                        failedResults.forEach(r => {
                                            resultHtml += `
                                                <div style="margin-bottom: 8px; padding: 8px; background: var(--bg-secondary); border-radius: 4px;">
                                                    <div><strong>第${r.index}个 (${r.duration}s)</strong></div>
                                                    <div style="font-size: 12px; color: var(--error-color);">错误: ${r.error}</div>
                                                </div>
                                            `;
                                        });

                                        resultHtml += `</div></div>`;
                                    }

                                    resultHtml += `
                                            <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 15px;">
                                                成功的草稿已保存到剪映项目目录
                                            </div>
                                            <button class="btn btn-primary" onclick="app.hideBatchProgress()">确定</button>
                                        </div>
                                    `;

                                    const progressDiv = document.getElementById('batch-progress');
                                    if (progressDiv) {
                                        progressDiv.innerHTML = resultHtml;
                                    }
                                } else if (status.error) {
                                    this.hideBatchProgress();
                                    this.showAlert('批量混剪失败: ' + status.error, 'error');
                                } else {
                                    this.hideBatchProgress();
                                }
                            }
                        }
                    } catch (error) {
                        console.error('轮询状态失败:', error);
                    }
                }, 2000); // 每2秒轮询一次
            }

            selectProduct(productName) {
                // 更新单个混剪的产品选择
                this.setInputValue('selected_product', productName);
                // 更新批量混剪的产品选择
                this.setInputValue('batch_selected_product', productName);
                this.showAlert('已选择产品: ' + productName, 'success');
            }

            async loadProductsForAutomix(section) {
                try {
                    const response = await fetch('/api/products');
                    const result = await response.json();

                    if (!result.success) {
                        this.showAlert('加载产品失败: ' + result.error, 'error');
                        return;
                    }

                    // 更新统一混剪的产品选择下拉框
                    const unifiedSelect = document.getElementById('unified_product');
                    if (unifiedSelect) {
                        unifiedSelect.innerHTML = '<option value="">请选择产品...</option>';
                        result.products.forEach(product => {
                            const option = document.createElement('option');
                            option.value = product.name;
                            option.textContent = product.name;
                            unifiedSelect.appendChild(option);
                        });
                    }

                    // 兼容旧版本：更新单个混剪的产品选择下拉框（如果存在）
                    const singleSelect = document.getElementById('selected_product');
                    if (singleSelect) {
                        singleSelect.innerHTML = '<option value="">请选择产品...</option>';
                        result.products.forEach(product => {
                            const option = document.createElement('option');
                            option.value = product.name;
                            option.textContent = product.name;
                            singleSelect.appendChild(option);
                        });
                    }

                    // 兼容旧版本：更新批量混剪的产品选择下拉框（如果存在）
                    const batchSelect = document.getElementById('batch_selected_product');
                    if (batchSelect) {
                        batchSelect.innerHTML = '<option value="">请选择产品...</option>';
                        result.products.forEach(product => {
                            const option = document.createElement('option');
                            option.value = product.name;
                            option.textContent = product.name;
                            batchSelect.appendChild(option);
                        });
                    }

                    // 如果是产品选择页面，更新产品列表
                    if (section === 'automix-products') {
                        const productsList = document.getElementById('products-list');
                        if (productsList) {
                            if (result.products.length === 0) {
                                productsList.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">未找到可用产品</div>';
                            } else {
                                productsList.innerHTML = result.products.map(product => `
                                    <div class="card" style="padding: 15px; text-align: center;">
                                        <div style="font-size: 18px; margin-bottom: 10px;">📦</div>
                                        <div style="font-weight: 600; margin-bottom: 5px;">${product.name}</div>
                                        <div style="font-size: 12px; color: var(--text-secondary); margin-bottom: 10px; word-break: break-all;">${product.path}</div>
                                        <button class="btn btn-primary" onclick="app.selectProduct('${product.name}')" style="width: 100%;">选择</button>
                                    </div>
                                `).join('');
                            }
                        }
                    }
                } catch (error) {
                    this.showAlert('加载产品失败: ' + error.message, 'error');
                }
            }

            // 特效排除管理功能
            async searchEffects() {
                const searchTerm = this.getInputValue('effect-search');
                const effectType = this.getInputValue('effect-type');

                try {
                    const response = await fetch('/api/effects/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            search_term: searchTerm,
                            effect_type: effectType
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.displayEffectsList(result.effects);
                        this.showAlert(`找到 ${result.effects.length} 个特效`, 'success');
                    } else {
                        this.showAlert('搜索特效失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('搜索特效失败: ' + error.message, 'error');
                }
            }

            displayEffectsList(effects) {
                const effectsList = document.getElementById('effects-list');
                if (!effectsList) return;

                if (effects.length === 0) {
                    effectsList.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">未找到匹配的特效</p>';
                    return;
                }

                effectsList.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; margin-bottom: 10px;">
                            <input type="checkbox" id="select-all-effects" onchange="app.toggleSelectAll()" style="margin-right: 8px;">
                            <span style="font-weight: 600;">全选/取消全选</span>
                        </label>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px;">
                        ${effects.map(effect => `
                            <label style="display: flex; align-items: center; padding: 8px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; transition: var(--transition);"
                                   onmouseover="this.style.backgroundColor='var(--bg-primary)'"
                                   onmouseout="this.style.backgroundColor='transparent'">
                                <input type="checkbox" class="effect-checkbox" value="${effect.id}" style="margin-right: 8px;">
                                <div style="flex: 1;">
                                    <div style="font-weight: 500; margin-bottom: 2px;">${effect.name}</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        ${effect.type} ${effect.excluded ? '(已排除)' : '(可用)'}
                                    </div>
                                </div>
                                <div style="font-size: 12px; color: ${effect.excluded ? 'var(--error-color)' : 'var(--accent-color)'};">
                                    ${effect.excluded ? '❌' : '✅'}
                                </div>
                            </label>
                        `).join('')}
                    </div>
                `;
            }

            toggleSelectAll() {
                const selectAllCheckbox = document.getElementById('select-all-effects');
                const effectCheckboxes = document.querySelectorAll('.effect-checkbox');

                effectCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            }

            async excludeSelected() {
                const selectedEffects = this.getSelectedEffects();

                if (selectedEffects.length === 0) {
                    this.showAlert('请先选择要排除的特效', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/effects/exclude', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            effect_ids: selectedEffects
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert(`成功排除 ${selectedEffects.length} 个特效`, 'success');
                        this.searchEffects(); // 刷新列表
                        this.loadExclusionStats(); // 更新统计
                    } else {
                        this.showAlert('排除特效失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('排除特效失败: ' + error.message, 'error');
                }
            }

            async includeSelected() {
                const selectedEffects = this.getSelectedEffects();

                if (selectedEffects.length === 0) {
                    this.showAlert('请先选择要包含的特效', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/effects/include', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            effect_ids: selectedEffects
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert(`成功包含 ${selectedEffects.length} 个特效`, 'success');
                        this.searchEffects(); // 刷新列表
                        this.loadExclusionStats(); // 更新统计
                    } else {
                        this.showAlert('包含特效失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('包含特效失败: ' + error.message, 'error');
                }
            }

            getSelectedEffects() {
                const checkboxes = document.querySelectorAll('.effect-checkbox:checked');
                return Array.from(checkboxes).map(cb => cb.value);
            }

            async resetAllExclusions() {
                if (!confirm('确定要重置所有排除设置吗？这将清除所有已排除的特效。')) {
                    return;
                }

                try {
                    const response = await fetch('/api/effects/reset', {
                        method: 'POST'
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert('已重置所有排除设置', 'success');
                        this.searchEffects(); // 刷新列表
                        this.loadExclusionStats(); // 更新统计
                        this.refreshSmartStats(); // 更新智能排除统计
                    } else {
                        this.showAlert('重置失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('重置失败: ' + error.message, 'error');
                }
            }

            // 滤镜管理功能
            async searchFilters() {
                const searchTerm = this.getInputValue('filter-search');
                const category = this.getInputValue('filter-category');

                try {
                    const response = await fetch('/api/filters/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            search_term: searchTerm,
                            category: category
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.displayFiltersList(result.filters);
                        this.showAlert(`找到 ${result.filters.length} 个滤镜`, 'success');
                    } else {
                        this.showAlert('搜索滤镜失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('搜索滤镜失败: ' + error.message, 'error');
                }
            }

            displayFiltersList(filters) {
                const filtersList = document.getElementById('filters-list');
                if (!filtersList) return;

                if (filters.length === 0) {
                    filtersList.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">未找到匹配的滤镜</p>';
                    return;
                }

                filtersList.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; margin-bottom: 10px;">
                            <input type="checkbox" id="select-all-filters" onchange="app.toggleSelectAllFilters()" style="margin-right: 8px;">
                            <span style="font-weight: 600;">全选/取消全选</span>
                        </label>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 10px;">
                        ${filters.map(filter => `
                            <label style="display: flex; align-items: center; padding: 8px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; transition: var(--transition);"
                                   onmouseover="this.style.backgroundColor='var(--bg-primary)'"
                                   onmouseout="this.style.backgroundColor='transparent'">
                                <input type="checkbox" class="filter-checkbox" value="${filter.id}" style="margin-right: 8px;">
                                <div style="flex: 1;">
                                    <div style="font-weight: 500; margin-bottom: 2px;">${filter.name}</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        ${filter.category} ${filter.excluded ? '(已排除)' : '(可用)'}
                                    </div>
                                </div>
                                <div style="font-size: 12px; color: ${filter.excluded ? 'var(--error-color)' : 'var(--accent-color)'};">
                                    ${filter.excluded ? '❌' : '✅'}
                                </div>
                            </label>
                        `).join('')}
                    </div>
                `;
            }

            toggleSelectAllFilters() {
                const selectAllCheckbox = document.getElementById('select-all-filters');
                const filterCheckboxes = document.querySelectorAll('.filter-checkbox');

                filterCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            }

            async saveFilterSettings() {
                const minIntensity = this.getInputValue('filter-min-intensity');
                const maxIntensity = this.getInputValue('filter-max-intensity');

                try {
                    const response = await fetch('/api/filters/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            min_intensity: parseInt(minIntensity),
                            max_intensity: parseInt(maxIntensity)
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert('滤镜设置保存成功', 'success');
                        this.setElementText('filters-intensity', `${minIntensity}-${maxIntensity}%`);
                    } else {
                        this.showAlert('保存设置失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('保存设置失败: ' + error.message, 'error');
                }
            }

            async refreshFilterStats() {
                try {
                    const response = await fetch('/api/exclusions');
                    const stats = await response.json();

                    if (stats.success !== false) {
                        const available = 468 - (stats.filters?.excluded || 0);
                        this.setElementText('filters-available', available);
                        this.setElementText('filters-excluded-count', stats.filters?.excluded || 0);
                        this.showAlert('滤镜统计已更新', 'success');
                    }
                } catch (error) {
                    this.showAlert('刷新统计失败: ' + error.message, 'error');
                }
            }

            // 转场管理功能
            async searchTransitions() {
                const searchTerm = this.getInputValue('transition-search');
                const type = this.getInputValue('transition-type');

                try {
                    const response = await fetch('/api/transitions/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            search_term: searchTerm,
                            type: type
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.displayTransitionsList(result.transitions);
                        this.showAlert(`找到 ${result.transitions.length} 个转场`, 'success');
                    } else {
                        this.showAlert('搜索转场失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('搜索转场失败: ' + error.message, 'error');
                }
            }

            displayTransitionsList(transitions) {
                const transitionsList = document.getElementById('transitions-list');
                if (!transitionsList) return;

                if (transitions.length === 0) {
                    transitionsList.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">未找到匹配的转场</p>';
                    return;
                }

                transitionsList.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; margin-bottom: 10px;">
                            <input type="checkbox" id="select-all-transitions" onchange="app.toggleSelectAllTransitions()" style="margin-right: 8px;">
                            <span style="font-weight: 600;">全选/取消全选</span>
                        </label>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 10px;">
                        ${transitions.map(transition => `
                            <label style="display: flex; align-items: center; padding: 8px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; transition: var(--transition);"
                                   onmouseover="this.style.backgroundColor='var(--bg-primary)'"
                                   onmouseout="this.style.backgroundColor='transparent'">
                                <input type="checkbox" class="transition-checkbox" value="${transition.id}" style="margin-right: 8px;">
                                <div style="flex: 1;">
                                    <div style="font-weight: 500; margin-bottom: 2px;">${transition.name}</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        ${transition.type} ${transition.excluded ? '(已排除)' : '(可用)'}
                                    </div>
                                </div>
                                <div style="font-size: 12px; color: ${transition.excluded ? 'var(--error-color)' : 'var(--accent-color)'};">
                                    ${transition.excluded ? '❌' : '✅'}
                                </div>
                            </label>
                        `).join('')}
                    </div>
                `;
            }

            toggleSelectAllTransitions() {
                const selectAllCheckbox = document.getElementById('select-all-transitions');
                const transitionCheckboxes = document.querySelectorAll('.transition-checkbox');

                transitionCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            }

            async saveTransitionSettings() {
                const minDuration = this.getInputValue('transition-min-duration');
                const maxDuration = this.getInputValue('transition-max-duration');
                const probability = this.getInputValue('transition-probability');
                const maxConsecutive = this.getInputValue('max-consecutive-transitions');

                try {
                    const response = await fetch('/api/transitions/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            min_duration: parseFloat(minDuration),
                            max_duration: parseFloat(maxDuration),
                            probability: parseInt(probability),
                            max_consecutive: parseInt(maxConsecutive)
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert('转场设置保存成功', 'success');
                        this.setElementText('transition-duration', `${minDuration}-${maxDuration}s`);
                    } else {
                        this.showAlert('保存设置失败: ' + result.error, 'error');
                    }
                } catch (error) {
                    this.showAlert('保存设置失败: ' + error.message, 'error');
                }
            }

            async refreshTransitionStats() {
                try {
                    const response = await fetch('/api/exclusions');
                    const stats = await response.json();

                    if (stats.success !== false) {
                        const available = 362 - (stats.transitions?.excluded || 0);
                        this.setElementText('transitions-available', available);
                        this.setElementText('transitions-excluded-count', stats.transitions?.excluded || 0);
                        this.showAlert('转场统计已更新', 'success');
                    }
                } catch (error) {
                    this.showAlert('刷新统计失败: ' + error.message, 'error');
                }
            }

            // 系统监控功能
            async loadSystemStatus() {
                try {
                    const response = await fetch('/api/system/status');
                    const data = await response.json();

                    if (data.success) {
                        this.updateSystemStatusDisplay(data);
                    } else {
                        console.error('获取系统状态失败:', data.error);
                    }
                } catch (error) {
                    console.error('获取系统状态失败:', error);
                }
            }

            updateSystemStatusDisplay(data) {
                // 更新系统状态
                const statusElement = document.getElementById('system-status');
                const statusTextElement = document.getElementById('system-status-text');
                if (statusElement && statusTextElement) {
                    statusElement.textContent = data.system_status.status_icon;
                    statusTextElement.textContent = data.system_status.status_text;
                }

                // 更新活跃任务数
                this.setElementText('active-tasks', data.active_tasks);

                // 更新已完成任务数（带变化显示）
                const completedTasksElement = document.getElementById('completed-tasks');
                const completedTasksChangeElement = document.getElementById('completed-tasks-change');
                if (completedTasksElement) {
                    const newValue = data.completed_today;
                    completedTasksElement.textContent = newValue;

                    // 处理任务计数变化信息
                    if (completedTasksChangeElement && data.task_change) {
                        const taskChange = data.task_change;
                        const currentTime = Date.now() / 1000;

                        // 如果变化信息是最近5秒内的，显示变化
                        if (taskChange.timestamp && (currentTime - taskChange.timestamp) < 5) {
                            const oldCount = taskChange.old_count || 0;
                            const newCount = taskChange.new_count || 0;

                            if (newCount > oldCount) {
                                completedTasksChangeElement.textContent = `${oldCount} → ${newCount}`;
                                completedTasksChangeElement.style.color = 'var(--accent-color)';
                                completedTasksChangeElement.style.fontWeight = 'bold';

                                // 添加闪烁效果
                                completedTasksChangeElement.style.animation = 'pulse 1.5s ease-in-out';
                                setTimeout(() => {
                                    if (completedTasksChangeElement) {
                                        completedTasksChangeElement.style.animation = '';
                                        completedTasksChangeElement.style.fontWeight = 'normal';
                                        // 3秒后恢复为简单显示
                                        setTimeout(() => {
                                            if (completedTasksChangeElement) {
                                                completedTasksChangeElement.textContent = `总计: ${newCount}`;
                                                completedTasksChangeElement.style.color = 'var(--text-tertiary)';
                                            }
                                        }, 3000);
                                    }
                                }, 1500);
                            }
                        } else {
                            // 显示总计
                            completedTasksChangeElement.textContent = `总计: ${newValue}`;
                            completedTasksChangeElement.style.color = 'var(--text-tertiary)';
                            completedTasksChangeElement.style.fontWeight = 'normal';
                        }
                    } else if (completedTasksChangeElement) {
                        // 没有变化信息时的默认显示
                        completedTasksChangeElement.textContent = `总计: ${newValue}`;
                        completedTasksChangeElement.style.color = 'var(--text-tertiary)';
                        completedTasksChangeElement.style.fontWeight = 'normal';
                    }
                }

                // 更新错误次数
                this.setElementText('error-count', data.error_count);

                // 更新当前操作
                this.setElementText('current-operation-text', data.current_operation);

                // 更新进度条
                const progressSection = document.getElementById('progress-section');
                const progressBar = document.getElementById('monitor-progress-bar');
                const progressText = document.getElementById('progress-text');

                if (data.progress > 0 && data.active_tasks > 0) {
                    if (progressSection) progressSection.style.display = 'block';
                    if (progressBar) progressBar.style.width = data.progress + '%';
                    if (progressText) progressText.textContent = data.progress + '%';
                } else {
                    if (progressSection) progressSection.style.display = 'none';
                }

                // 更新操作日志
                this.updateOperationLogs(data.logs);
            }

            updateOperationLogs(logs) {
                const logsContainer = document.getElementById('operation-logs');
                if (!logsContainer) return;

                if (logs.length === 0) {
                    logsContainer.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">暂无操作日志</div>';
                    return;
                }

                const logsHtml = logs.map(log => {
                    const typeColor = {
                        'info': 'var(--primary-color)',
                        'success': 'var(--accent-color)',
                        'warning': 'var(--warning-color)',
                        'error': 'var(--error-color)'
                    }[log.type] || 'var(--text-secondary)';

                    const typeIcon = {
                        'info': 'ℹ️',
                        'success': '✅',
                        'warning': '⚠️',
                        'error': '❌'
                    }[log.type] || '📝';

                    return `
                        <div style="display: flex; align-items: center; padding: 8px 0; border-bottom: 1px solid var(--border-color);">
                            <div style="color: ${typeColor}; margin-right: 8px;">${typeIcon}</div>
                            <div style="flex: 1;">
                                <div style="font-size: 14px;">${log.message}</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">${log.time}</div>
                            </div>
                        </div>
                    `;
                }).join('');

                logsContainer.innerHTML = logsHtml;
            }

            startSystemMonitoring() {
                // 防止重复启动
                if (this.systemMonitorInterval) {
                    return;
                }

                // 立即加载一次
                this.loadSystemStatus();

                // 使用优化后的间隔时间（10秒）
                this.systemMonitorInterval = setInterval(() => {
                    // 只在页面可见时更新
                    if (!document.hidden) {
                        this.loadSystemStatus();
                    }
                }, this.systemMonitorDelay);
            }

            stopSystemMonitoring() {
                if (this.systemMonitorInterval) {
                    clearInterval(this.systemMonitorInterval);
                    this.systemMonitorInterval = null;
                }
            }

            // 统一混剪相关方法
            updateMixMode() {
                const count = parseInt(this.getInputValue('unified_count')) || 1;
                const modeIndicator = document.getElementById('mode-indicator');
                const startButtonText = document.getElementById('start-button-text');
                const singleDurationGroup = document.getElementById('single-duration-group');
                const batchDurationGroup = document.getElementById('batch-duration-group');
                const progressCount = document.getElementById('unified-progress-count');

                if (count === 1) {
                    // 单个混剪模式
                    if (modeIndicator) modeIndicator.innerHTML = '🎯 单个混剪模式';
                    if (startButtonText) startButtonText.innerHTML = '🎬 开始生成混剪视频';
                    if (singleDurationGroup) singleDurationGroup.style.display = 'block';
                    if (batchDurationGroup) batchDurationGroup.style.display = 'none';
                    if (progressCount) progressCount.textContent = '1/1';
                } else {
                    // 批量生成模式
                    if (modeIndicator) modeIndicator.innerHTML = `📊 批量生成模式 (${count}个视频)`;
                    if (startButtonText) startButtonText.innerHTML = `📊 开始批量生成 ${count} 个视频`;
                    if (singleDurationGroup) singleDurationGroup.style.display = 'none';
                    if (batchDurationGroup) batchDurationGroup.style.display = 'block';
                    if (progressCount) progressCount.textContent = `0/${count}`;
                }
            }

            async startUnifiedAutomix() {
                const product = this.getInputValue('unified_product');
                const count = parseInt(this.getInputValue('unified_count')) || 1;

                if (!product) {
                    this.showAlert('请先选择产品', 'warning');
                    return;
                }

                try {
                    let response;
                    if (count === 1) {
                        // 单个混剪
                        const duration = parseInt(this.getInputValue('unified_duration')) || 35;
                        response = await fetch('/api/automix/single', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                product: product,
                                duration: duration
                            })
                        });
                    } else {
                        // 批量生成
                        const minDuration = parseInt(this.getInputValue('unified_min_duration')) || 30;
                        const maxDuration = parseInt(this.getInputValue('unified_max_duration')) || 40;
                        response = await fetch('/api/automix/batch', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                product: product,
                                count: count,
                                min_duration: minDuration,
                                max_duration: maxDuration
                            })
                        });
                    }

                    const result = await response.json();

                    if (result.success) {
                        this.showAlert(result.message, 'success');
                        this.showUnifiedProgress();
                        this.startUnifiedProgressPolling();
                    } else {
                        this.showAlert(result.error || '启动失败', 'error');
                    }
                } catch (error) {
                    console.error('启动混剪失败:', error);
                    this.showAlert('启动混剪失败: ' + error.message, 'error');
                }
            }

            showUnifiedProgress() {
                const progressDiv = document.getElementById('unified-progress');
                if (progressDiv) {
                    progressDiv.style.display = 'block';
                }
            }

            hideUnifiedProgress() {
                const progressDiv = document.getElementById('unified-progress');
                if (progressDiv) {
                    progressDiv.style.display = 'none';
                }
            }

            startUnifiedProgressPolling() {
                // 清除现有的轮询
                if (this.statusPollInterval) {
                    clearInterval(this.statusPollInterval);
                }

                // 使用优化后的轮询间隔（3秒）
                this.statusPollInterval = setInterval(async () => {
                    try {
                        const response = await fetch('/api/status');
                        const status = await response.json();
                        this.updateUnifiedProgress(status);

                        // 如果任务完成，停止轮询
                        if (!status.running) {
                            clearInterval(this.statusPollInterval);
                            this.statusPollInterval = null;

                            setTimeout(() => {
                                this.hideUnifiedProgress();
                            }, 3000); // 3秒后隐藏进度条
                        }
                    } catch (error) {
                        console.error('获取状态失败:', error);
                    }
                }, this.statusPollDelay);
            }

            updateUnifiedProgress(status) {
                const progressText = document.getElementById('unified-progress-text');
                const progressBar = document.getElementById('unified-progress-bar');
                const progressCount = document.getElementById('unified-progress-count');

                if (progressText) {
                    progressText.textContent = status.progress || '处理中...';
                }

                if (progressCount && status.total_count) {
                    progressCount.textContent = `${status.current_count || 0}/${status.total_count}`;
                }

                if (progressBar) {
                    let progress = 0;
                    if (status.total_count && status.total_count > 0) {
                        progress = ((status.current_count || 0) / status.total_count) * 100;
                    }
                    progressBar.style.width = `${progress}%`;
                }

                // 更新混剪统计信息
                this.updateAutomixStats(status);

                // 如果有错误，显示错误信息
                if (status.error) {
                    this.showAlert('混剪失败: ' + status.error, 'error');
                }
            }

            updateAutomixStats(status) {
                console.log('更新混剪统计:', status);

                // 显示统计面板
                const statsPanel = document.getElementById('automix-stats-panel');
                if (statsPanel && (status.running || status.result)) {
                    statsPanel.style.display = 'block';
                    console.log('显示统计面板');
                }

                // 如果有结果数据，更新统计信息
                if (status.result && status.result.statistics) {
                    const stats = status.result.statistics;
                    console.log('找到统计数据:', stats);

                    // 更新基础统计
                    this.setElementText('stats-total-materials', stats.total_materials || 0);
                    this.setElementText('stats-selected-materials', stats.selected_materials || 0);
                    this.setElementText('stats-applied-filters', stats.applied_filters || 0);
                    this.setElementText('stats-applied-effects', stats.applied_effects || 0);
                    this.setElementText('stats-applied-transitions', stats.applied_transitions || 0);
                    this.setElementText('stats-audio-tracks', stats.audio_tracks || 0);

                    // 更新产品信息
                    this.setElementText('stats-product-model', stats.product_model || '未知');

                    // 更新视频时长
                    if (status.result.duration) {
                        const duration = (status.result.duration / 1000000).toFixed(1);
                        this.setElementText('stats-video-duration', `${duration}s`);
                    }

                    // 计算和更新覆盖率
                    const videoCount = stats.selected_materials || 0;
                    const filterCount = stats.applied_filters || 0;
                    const effectCount = stats.applied_effects || 0;
                    const transitionCount = stats.applied_transitions || 0;

                    const filterCoverage = videoCount > 0 ? ((filterCount / videoCount) * 100).toFixed(1) : 0;
                    const effectCoverage = videoCount > 0 ? ((effectCount / videoCount) * 100).toFixed(1) : 0;
                    const transitionCoverage = videoCount > 1 ? ((transitionCount / (videoCount - 1)) * 100).toFixed(1) : 0;

                    this.setElementText('stats-filter-coverage', `${filterCoverage}%`);
                    this.setElementText('stats-effect-coverage', `${effectCoverage}%`);
                    this.setElementText('stats-transition-coverage', `${transitionCoverage}%`);
                }

                // 如果完成，显示结果
                if (status.result && !status.running) {
                    if (status.result.total_count && status.result.total_count > 1) {
                        // 批量生成结果
                        this.showAlert(`批量生成完成！成功: ${status.result.successful_count}个，失败: ${status.result.failed_count}个`, 'success');
                    } else {
                        // 单个混剪结果
                        this.showAlert(`混剪完成！草稿: ${status.result.draft_name}`, 'success');
                    }
                }
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            try {
                console.log('DOM加载完成，开始初始化应用...');
                window.app = new AutoMixApp();
                console.log('应用初始化完成');
            } catch (error) {
                console.error('应用初始化失败:', error);
                // 强制隐藏加载器
                const loader = document.getElementById('pageLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
                // 显示错误信息
                document.body.innerHTML += `
                    <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                background: #f44336; color: white; padding: 20px; border-radius: 8px; z-index: 10000;">
                        <h3>应用加载失败</h3>
                        <p>错误信息: ${error.message}</p>
                        <p>请刷新页面重试</p>
                    </div>
                `;
            }
        });

        // 备用加载器隐藏机制 - 如果3秒后仍未隐藏，强制隐藏
        setTimeout(() => {
            const loader = document.getElementById('pageLoader');
            if (loader && !loader.classList.contains('hidden')) {
                console.warn('备用机制：强制隐藏加载器');
                loader.style.display = 'none';

                // 确保主内容可见
                const appContainer = document.querySelector('.app-container');
                if (appContainer) {
                    appContainer.style.display = 'flex';
                }
            }
        }, 3000);
    </script>
</body>
</html>
