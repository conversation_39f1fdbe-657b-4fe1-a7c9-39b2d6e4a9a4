# 🎬 剪映自动混剪工具

> **智能视频混剪解决方案** - 基于pyJianYingDraft开发的专业级自动混剪工具

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)

一键生成专业级短视频，支持智能特效、防审核覆盖、字幕处理等完整功能。专为内容创作者和视频工作室设计。

## ✨ 核心亮点

### 🎯 智能化操作
- **交互式界面** - 直观的菜单式操作，零学习成本
- **一键混剪** - 自动扫描素材，智能生成30-40秒短视频
- **批量生成** - 支持一次性生成多个不同版本

### 🛡️ 防审核技术
- **Pexels覆盖层** - 5%不透明度风景视频覆盖，有效防止平台识别
- **智能时长匹配** - 自动调整覆盖层时长，完美适配目标视频
- **多重备用方案** - 网络失败时自动使用本地备用视频

### 🎨 专业特效系统
- **912种视频特效** - 涵盖各类专业特效，智能轻微应用
- **468种滤镜效果** - 色彩调节、风格化处理等多样选择
- **362种转场动画** - 流畅的场景切换，提升观看体验
- **轻微参数优化** - 12种参数类型智能调节，避免过度强烈效果
- **特效排除管理** - 可排除不喜欢的效果，个性化定制

### 📝 智能字幕处理
- **多编码支持** - 智能检测UTF-8、GBK、GB2312等编码，修复windows-1252误检测
- **泰语字幕支持** - 完美支持CP874、ISO-8859-11、TIS-620等泰语编码
- **时间戳修复** - 自动修复SRT格式错误，只修复时间不改内容
- **内容完全保护** - 绝不修改字幕文本，保持原始内容100%完整
- **多语言兼容** - 支持中英文混合、泰语、特殊字符、HTML标签

### 🎵 专业音频处理
- **双轨道设计** - 解说音频100%音量，背景音频10%音量
- **智能静音** - 视频轨道自动静音，避免音频冲突
- **环境音效** - 可选添加环境音效，增强沉浸感

## 🚀 快速开始

### 📋 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: 3.8+
- **剪映版本**: 剪映专业版
- **网络连接**: 防审核功能需要（可选）

### ⚡ 一键启动

#### 🖥️ 命令行界面（推荐新手）

```bash
python interactive_automix.py
```

#### 🌐 Web界面（推荐高级用户）

```bash
python web_interface.py
```

然后在浏览器中访问：`http://localhost:5001`

### 🎮 功能菜单

```
🎬 剪映自动混剪工具
============================================================
📋 主菜单:
1. 🎬 自动混剪          - 智能生成混剪视频
2. 📊 批量生成          - 一键生成多个版本
3. 🚫 特效排除管理      - 个性化特效选择
4. 🛡️ Pexels防审核设置  - 防审核参数调整
5. ⚙️ 查看配置信息      - 当前配置概览
6. 🔧 修改配置          - 自定义参数设置
7. ❓ 帮助信息          - 详细使用说明
0. 🚪 退出程序
```

## 🌐 Web交互界面

### 🎯 界面优势

Web界面提供更直观的可视化操作体验，特别适合：
- 需要精确控制特效排除的用户
- 喜欢图形化界面的用户
- 需要远程访问的用户
- 多人协作的团队

### 🚀 启动Web界面

```bash
python web_interface.py
```

启动成功后，在浏览器中访问：`http://localhost:5001`

### 🎨 主要功能

#### 📊 配置管理
- **可视化配置面板** - 直观调整所有混剪参数
- **实时预览** - 配置更改即时生效
- **参数验证** - 自动检查配置有效性
- **一键重置** - 快速恢复默认设置

#### 🎬 混剪操作
- **产品选择器** - 下拉菜单选择产品型号
- **批量生成** - 可视化设置生成数量和参数
- **进度监控** - 实时显示混剪进度和状态
- **结果预览** - 生成完成后直接预览结果

#### 🚫 智能特效管理
- **搜索补全** - 输入关键词自动搜索匹配特效
- **分类管理** - 滤镜、特效、转场独立管理
- **智能排除** - 一键排除夸张特效，保持专业风格
- **实时统计** - 显示可用和已排除特效数量

#### 🛡️ 防审核设置
- **Pexels集成** - 可视化配置防审核覆盖层
- **参数调整** - 滑块调整不透明度和其他参数
- **预览功能** - 实时预览防审核效果
- **状态监控** - 显示API连接状态和可用性

### 🔧 特效排除管理详解

#### 🔍 智能搜索
```
输入框功能：
• 实时搜索 - 输入关键词即时显示匹配结果
• 高亮显示 - 匹配的关键词用黄色背景高亮
• 键盘导航 - 支持上下箭头选择，回车确认
• 模糊匹配 - 包含关键词即可匹配
```

#### 📝 搜索示例
- 搜索 **"光"** → 找到106个特效（ktv灯光、丁达尔光线、光斑虚化等）
- 搜索 **"旋转"** → 找到19个转场（中心旋转、抠像旋转、旋转圆球等）
- 搜索 **"复古"** → 找到1个滤镜（复古工业）

#### 🚫 智能排除功能
一键排除183个夸张特效，包括：
- **恐怖/惊悚类**: 恐怖故事、恐怖涂鸦、诡异分割
- **过度卡通类**: 爱心系列、emoji钻石、仙女变身
- **过度复杂类**: 九屏、分屏、迷幻系列
- **低质量类**: 故障、像素、马赛克系列
- **过时类**: 70s、90s、DV、VHS系列
- **社交界面类**: ins界面、windows弹窗
- **文字类**: I Love You、文字闪动系列
- **恶搞类**: 不对劲、中枪了、乌鸦飞过

### 💡 使用技巧

#### 🎯 高效操作
1. **首次使用** - 建议先运行智能排除，获得专业特效库
2. **精确控制** - 使用搜索功能精确添加/移除特效
3. **批量操作** - 利用智能排除快速建立基础排除列表
4. **实时调整** - 在混剪过程中随时调整特效设置

#### 🔄 工作流建议
```
1. 启动Web界面 → 2. 配置基础参数 → 3. 智能排除夸张特效
→ 4. 精细调整特效 → 5. 设置防审核 → 6. 开始混剪
```

## 📁 素材库结构

### 🗂️ 推荐目录组织

基于实际项目的最佳实践，推荐以下目录结构：

```
📁 素材库根目录/
├── 📂 A83/                     # 产品型号文件夹
│   ├── 📂 摆拍/                 # 产品摆拍素材
│   │   ├── 🎥 摆拍 (1).mp4     # 编号命名的视频文件
│   │   ├── 🎥 摆拍 (2).mp4     # 支持.mp4/.mov格式
│   │   └── 🎥 摆拍 (3).mov
│   ├── 📂 使用场景/             # 实际使用场景
│   │   ├── 🎥 使用场景 (1).mp4
│   │   ├── 🎥 使用场景 (2).mp4
│   │   └── 🎥 A_modern_courtyard.mp4  # 描述性命名
│   ├── 📂 支架/                 # 产品配件展示
│   │   ├── 🎥 支架 (1).mp4
│   │   ├── 🎥 支架展示 (1).mp4
│   │   └── 🎥 支架展示 (2).mp4
│   ├── 📂 车间/                 # 生产制造场景
│   │   ├── 🎥 车间 (1).mov
│   │   └── 🎥 车间 (2).mov
│   ├── 📂 防水/                 # 功能演示
│   │   └── 🎥 防水.mov
│   ├── 📂 旋转/                 # 产品特写
│   │   ├── 🎥 旋转镜头.mov
│   │   ├── 🎥 旋转镜头1.mov
│   │   └── 🎥 扫码.mov
│   ├── 📂 软件操作演示/         # 软件界面录屏
│   │   ├── 🎥 app (1).mov
│   │   ├── 🎥 app.mp4
│   │   └── 🎥 screen-recording.mp4
│   ├── 📂 配音/                 # 解说音频文件
│   │   ├── 🎵 ElevenLabs_voice1.mp3
│   │   ├── 🎵 ElevenLabs_voice2.mp3
│   │   └── 🎵 narration.mp3
│   └── 📂 字幕/                 # SRT字幕文件
│       ├── 📄 code (11).srt
│       ├── 📄 code (12).srt
│       └── 📄 code5.srt
├── 📂 A84/                     # 其他产品型号
│   ├── 📂 摆拍/
│   ├── 📂 使用场景/
│   └── 📂 配音/
├── 📂 车载A1/                  # 车载产品系列
│   └── 📂 安装演示/
├── 📂 PAD702/                  # 平板产品系列
│   └── 📂 功能展示/
├── 📂 实拍实景/                # 通用实景素材
│   ├── 🎥 outdoor_scene1.mp4
│   └── 🎥 indoor_scene1.mp4
├── 📂 效果素材/                # 特效和装饰素材
│   ├── 🎥 transition1.mp4
│   └── 🎥 overlay1.mp4
└── 📂 音效/                    # 环境音效库
    ├── 🎵 calming-rain.mp3     # 雨声环境音
    ├── 🎵 typing-keyboard.mp3  # 键盘敲击音
    ├── 🎵 crow-sound.mp3       # 自然环境音
    └── 🎵 evening-village.mp3  # 乡村夜晚音
```

### 📝 目录命名规范

1. **产品型号文件夹**: 使用具体型号名称（如A83、A84、车载A1）
2. **场景分类文件夹**: 使用中文描述性名称（摆拍、使用场景、支架等）
3. **视频文件**: 支持编号命名 `场景名 (1).mp4` 或描述性命名
4. **音频文件**: 配音文件放在 `配音/` 文件夹，环境音放在 `音效/` 文件夹
5. **字幕文件**: SRT格式，放在 `字幕/` 文件夹中

### 📋 文件类型支持

| 类型 | 支持格式 | 说明 |
|------|----------|------|
| **视频** | MP4, AVI, MOV, MKV | 主要素材，自动去前3秒 |
| **音频** | MP3, WAV, AAC | 解说/背景音频 |
| **字幕** | SRT | 多编码支持，时间戳自动修复 |

## 🎯 核心功能详解

### 🎬 智能自动混剪

#### 📊 混剪流程
1. **素材扫描** - 自动识别产品型号和场景分类
2. **智能选择** - 从每个场景随机选择视频片段
3. **时长控制** - 精确控制30-40秒总时长
4. **效果应用** - 随机添加特效、滤镜、转场
5. **音频处理** - 双轨道音频，智能音量控制
6. **字幕集成** - SRT字幕自动解析和时间修复
7. **防审核处理** - 添加5%不透明度覆盖层

#### ⚙️ 智能参数
- **画面缩放**: 110% (防止黑边)
- **去除时长**: 前3秒 (跳过无效内容)
- **色彩调整**: 随机亮度/对比度变化
- **音频配比**: 解说100% + 背景10%

### 🛡️ 防审核技术

#### 🌐 Pexels覆盖层系统
- **自动搜索**: 智能搜索风景、自然场景视频
- **智能下载**: 高清MP4格式，自动缓存管理
- **时长适配**: 自动拉伸或截取以匹配目标时长
- **透明度控制**: 5%不透明度，既防审核又不影响观看

#### 🔄 多重备用机制
```
层级1: Pexels API在线下载
层级2: SSL错误时的备用下载
层级3: 网络失败时的本地备用视频
层级4: 完全跳过覆盖层继续混剪
```

### 🎨 专业特效系统

#### 📊 特效库统计
- **🎭 视频特效**: 912种 (粒子、光效、动画等)
- **🎨 滤镜效果**: 468种 (色彩、风格、质感等)
- **🔄 转场动画**: 362种 (淡入淡出、擦除、3D等)

#### 🎯 智能应用策略
- **概率控制**: 特效80%、滤镜90%、转场100%
- **轻微参数**: 滤镜强度10-25%随机变化（轻微效果）
- **智能参数**: 12种参数类型智能调节，包括纹理和滤镜参数
- **时间分布**: 每10-15秒随机调整参数
- **排除管理**: 可排除不喜欢的效果

#### 🎨 轻微特效参数系统
- **亮度参数**: 15-35 (轻微调整，避免过亮过暗)
- **对比度参数**: 20-40 (轻微调整)
- **饱和度参数**: 25-45 (轻微调整)
- **大小参数**: 10-30 (轻微缩放)
- **速度参数**: 25-45 (轻微变速)
- **强度参数**: 10-25 (轻微强度)
- **透明度参数**: 20-40 (轻微透明)
- **模糊参数**: 5-20 (轻微模糊)
- **旋转参数**: 10-30 (轻微旋转)
- **纹理参数**: 15-35 (轻微纹理效果) ⭐ 新增
- **滤镜参数**: 20-40 (轻微滤镜效果) ⭐ 新增
- **其他参数**: 中心25±8 (轻微正态分布)

### 📝 智能字幕系统

#### 🔤 编码智能检测
- **BOM检测**: UTF-8、UTF-16标记自动识别
- **多编码支持**: UTF-8、GBK、GB2312、Big5等
- **质量评分**: 智能评估解码质量选择最佳编码
- **备用机制**: 4层备用方案确保成功读取

#### 🕐 时间戳智能修复
```
修复类型              修复前                    修复后
时间单位错误    00:03:00,800 → 00:06:00,500    00:00:03,800 → 00:00:06,500
分隔符错误      00:00:03.800 → 00:00:06.500    00:00:03,800 → 00:00:06,500
箭头格式错误    00:00:03,800->00:00:06,500     00:00:03,800 → 00:00:06,500
缺失毫秒        00:00:03 → 00:00:06            00:00:03,000 → 00:00:06,000
```

#### 🛡️ 内容完全保护
- **零修改原则**: 绝不修改字幕文本内容
- **完整保留**: 特殊字符、HTML标签、多语言内容
- **格式保持**: 换行、空格、标点符号原样保留

## ⚙️ 配置管理

### 📊 主要配置项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| **素材库路径** | `F:\Windows_data\Videos\B日期分类\素材` | 视频素材根目录 |
| **草稿输出路径** | `C:\Users\<USER>\AppData\Local\JianyingPro\...` | 剪映草稿保存位置 |
| **视频时长范围** | 30-40秒 | 生成视频的目标时长 |
| **特效概率** | 80% | 应用特效的概率 |
| **滤镜概率** | 90% | 应用滤镜的概率 |
| **转场概率** | 100% | 应用转场的概率 |
| **画面缩放** | 110% | 视频缩放比例 |
| **去除前段** | 3秒 | 跳过视频开头时长 |

### 🛡️ 防审核配置

```ini
[JianYingDraft.automix]
# Pexels API配置
pexels_api_key=your_pexels_api_key_here  # 请替换为你的Pexels API密钥
pexels_overlay_opacity=0.05              # 5%不透明度
enable_pexels_overlay=true               # 启用防审核覆盖层

# 音频配置
narration_volume=1.0         # 解说音量100%
background_volume=0.1        # 背景音量10%
video_volume=0.0            # 视频静音
```

#### 🔑 Pexels API密钥获取

1. **访问Pexels官网**: https://www.pexels.com/api/
2. **注册账户**: 免费注册Pexels开发者账户
3. **申请API密钥**: 在开发者控制台申请免费API密钥
4. **配置密钥**: 将获取的密钥替换配置文件中的 `your_pexels_api_key_here`

> **💡 提示**: 工具内置了默认API密钥，可以直接使用。如需自定义或遇到限制，请申请个人密钥。

### 🎨 特效配置

```ini
# 特效概率控制
effect_probability=0.8       # 80%概率应用特效
filter_probability=0.9       # 90%概率应用滤镜
transition_probability=1.0   # 100%概率应用转场

# 滤镜强度控制（轻微效果）
filter_intensity_min=10      # 最小强度10%（更轻微）
filter_intensity_max=25      # 最大强度25%（更轻微）
```

## 📦 批量生成

### 🚀 一键批量生成

1. **启动批量模式**: 选择菜单 `2. 📊 批量生成`
2. **设置生成数量**: 输入要生成的视频数量（默认5个）
3. **自动化处理**: 系统自动生成多个不同版本
4. **版本差异化**: 每个版本使用不同的素材组合和特效

### 📊 批量生成特色

- **素材随机化**: 每次生成使用不同的素材组合
- **特效随机化**: 随机应用不同的特效、滤镜、转场
- **参数随机化**: 亮度、对比度、滤镜强度随机变化
- **时长控制**: 每个版本都精确控制在30-40秒

## 🔧 高级功能

### 🚫 特效排除管理

#### 功能特色
- **智能排除**: 可排除不喜欢的特效、滤镜、转场
- **实时生效**: 排除设置立即生效，不会再选择被排除的效果
- **批量操作**: 支持批量添加/移除排除项
- **持久保存**: 排除设置自动保存，重启后仍然有效

#### 操作流程
```
1. 查看已排除 → 显示当前排除列表
2. 添加排除   → 从可用效果中选择要排除的
3. 移除排除   → 恢复之前排除的效果
4. 清空排除   → 重置所有排除设置
```

### ⚙️ 配置管理系统

#### 实时配置调整
- **交互式修改**: 菜单式配置修改，直观易用
- **参数验证**: 自动验证配置值的有效性
- **实时生效**: 配置修改后立即生效，无需重启
- **配置备份**: 自动备份配置，防止意外丢失

## 🛠️ 技术架构

### 🏗️ 核心模块架构

```
📦 JianYingDraft/
├── 🎬 core/
│   ├── standardAutoMix.py      # 自动混剪核心引擎
│   ├── pexelsManager.py        # 防审核覆盖层管理
│   ├── srtProcessor.py         # 字幕处理系统
│   ├── configManager.py       # 配置管理系统
│   └── effectExclusionManager.py # 特效排除管理
├── 📚 pyJianYingDraft/         # 剪映草稿API库
└── 🛠️ utils/                   # 工具函数库
```

### 🔧 技术特色

- **模块化设计**: 高内聚低耦合的模块架构
- **智能错误处理**: 多层备用机制，确保稳定运行
- **性能优化**: 智能缓存和批处理，提升处理效率
- **扩展性强**: 易于添加新功能和特效

### 📚 核心依赖

| 库名 | 版本 | 用途 |
|------|------|------|
| **pyJianYingDraft** | 内置 | 剪映草稿文件操作 |
| **requests** | 2.25+ | HTTP请求和文件下载 |
| **configparser** | 内置 | 配置文件解析 |

## 🔍 故障排除

### ❓ 常见问题解决

#### Q: 程序启动失败？
**A**: 检查Python版本是否为3.8+，确保所有依赖已正确安装

#### Q: 素材扫描失败？
**A**: 检查素材库路径是否正确，确保目录结构符合要求

#### Q: 防审核覆盖层不工作？
**A**: 检查网络连接，验证Pexels API密钥是否有效

#### Q: 字幕显示乱码？
**A**: 字幕文件编码问题，系统会自动检测和修复

#### Q: 生成的草稿无法在剪映中打开？
**A**: 检查剪映安装路径和草稿输出路径是否正确

### 🔧 调试模式

启动时添加调试参数：
```bash
python interactive_automix.py --debug
```

### 📋 日志文件

- **位置**: `automix.log`
- **内容**: 详细的处理过程和错误信息
- **用途**: 问题诊断和性能分析

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献指南

### 🎯 贡献方式

1. **Fork** 本仓库
2. **创建** 功能分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

### 📋 贡献规范

- 代码风格遵循 PEP 8
- 提交信息使用中文，格式清晰
- 新功能需要添加相应的测试
- 重大更改需要更新文档

## 📞 技术支持

### 🆘 获取帮助

1. **📖 查看文档**: 详细阅读本README和功能清单
2. **🔍 检查配置**: 确认所有配置项设置正确
3. **📋 查看日志**: 检查automix.log中的错误信息
4. **🐛 提交Issue**: 在GitHub上描述问题和环境信息

### 📧 联系方式

- **GitHub Issues**: [提交问题](https://github.com/miludeerforest/JianYingProDraft/issues)
- **功能建议**: 欢迎提交新功能建议和改进意见

---

## 🎯 界面对比

### 🖥️ 命令行界面 vs 🌐 Web界面

| 特性 | 命令行界面 | Web界面 |
|------|------------|---------|
| **适用用户** | 新手用户 | 高级用户 |
| **操作方式** | 菜单选择 | 可视化操作 |
| **特效管理** | 基础排除 | 智能搜索+排除 |
| **配置调整** | 逐项设置 | 可视化面板 |
| **进度监控** | 文本显示 | 实时图表 |
| **多任务** | 单任务 | 支持多任务 |
| **远程访问** | ❌ | ✅ |
| **学习成本** | 低 | 中等 |

### 💡 选择建议

- **首次使用** → 推荐命令行界面，快速上手
- **频繁使用** → 推荐Web界面，效率更高
- **精确控制** → 推荐Web界面，功能更丰富
- **团队协作** → 推荐Web界面，支持远程访问

## 🎉 开始创作

**现在就开始使用剪映自动混剪工具，让AI为你创作专业级短视频！**

```bash
# 克隆项目
git clone https://github.com/miludeerforest/JianYingProDraft.git

# 进入目录
cd JianYingProDraft

# 启动命令行界面（推荐新手）
python interactive_automix.py

# 或启动Web界面（推荐高级用户）
python web_interface.py
# 然后访问 http://localhost:5001
```

**🚀 双界面支持，智能混剪，让创作更简单！**

---

## 📋 更新日志

### 🎯 v2.2.0 - 界面优化版 (2025-07-24)

#### 🎨 用户体验重大改进
- **🔄 统一混剪界面** - 解决单个混剪与批量混剪功能重复问题
- **🎯 智能模式切换** - 根据生成数量自动切换单个/批量模式
- **📊 动态界面提示** - 实时显示当前模式和预期结果
- **🎮 简化操作流程** - 用户只需设置视频数量，系统自动处理

#### 🚀 界面结构优化
- **菜单简化**: 将"单个混剪"和"批量生成"合并为"开始混剪"
- **产品管理**: 重命名"产品选择"为"产品管理"，功能更清晰
- **快捷操作**: 优化快速操作按钮，统一混剪入口

#### 🎛️ 智能参数控制
- **单个模式** (数量=1):
  - 🎯 显示"单个混剪模式"提示
  - ⏱️ 固定时长设置
  - 🎬 按钮文本："开始生成混剪视频"
- **批量模式** (数量>1):
  - 📊 显示"批量生成模式 (X个视频)"提示
  - ⏱️ 时长范围设置（每个视频随机选择）
  - 📊 按钮文本："开始批量生成 X 个视频"

#### 🔧 技术实现优化
- **后端兼容性**: 保留原有API接口，确保功能完整性
- **前端智能路由**: 根据数量参数自动选择调用接口
- **统一进度显示**: 智能适配单个/批量进度显示格式
- **状态管理**: 优化任务状态跟踪和用户反馈

#### 💡 用户体验提升
- **消除困惑**: 用户不再需要在相似功能间选择
- **操作直观**: 想要几个视频就设置几个数量
- **界面统一**: 一个界面满足所有混剪需求
- **实时反馈**: 动态显示当前模式和参数要求

### 🎨 v2.1.0 - 现代化界面版 (2025-07-23)

#### ✨ 界面重构与现代化设计
- **🎨 现代化设计系统** - 全新色彩系统，支持渐变色和多层次颜色
- **🚀 性能优化架构** - DOM缓存、事件委托、懒加载机制
- **📱 响应式设计** - 优化移动端和桌面端体验
- **⌨️ 键盘快捷键** - 支持Ctrl+1-5快速切换菜单
- **🎯 用户体验提升** - 现代化加载器、悬停效果、动画过渡

#### 🐛 进度提示系统修复
- **📊 批量生成进度** - 修复进度计数不更新问题 (0/5 → 实时更新)
- **🎬 单个混剪进度** - 添加进度条百分比显示
- **🔄 轮询机制优化** - 删除重复方法，优化轮询间隔
- **💾 状态管理增强** - 后端添加current_count和total_count字段

#### 🎯 界面组件美化
- **🃏 卡片系统** - 现代化卡片设计，悬停效果和装饰条
- **🔘 按钮组件** - 渐变色按钮，光泽效果和阴影
- **📝 表单控件** - 优化输入框、滑块和选择器样式
- **🎨 色彩系统** - 统一的CSS变量系统，支持主题色

#### ⚡ 性能优化技术
- **🧠 智能缓存** - DOM元素缓存和页面内容缓存
- **🎯 事件委托** - 优化事件处理性能
- **⏱️ 防抖搜索** - 300ms防抖延迟，减少API调用
- **👁️ 可见性检查** - 只在页面可见时进行轮询更新

#### 🔧 技术架构改进
- **📦 模块化设计** - 清晰的功能模块分离
- **🛡️ 错误处理** - 完善的异常处理和用户提示
- **🔄 状态同步** - 前后端状态实时同步
- **📊 统计显示** - 实时任务统计和进度监控

### 🎉 v2.1.0 - 轻微特效优化版 (2025-07-05)

#### 🎨 特效参数优化
- **轻微特效系统** - 全面优化特效参数，避免过度强烈的视觉效果
- **智能参数调节** - 12种参数类型智能识别和调节
- **纹理参数支持** - 新增纹理参数类型，范围15-35
- **滤镜参数支持** - 新增滤镜参数类型，范围20-40
- **参数范围优化** - 所有参数均控制在轻微效果范围内

#### 🔧 编码检测修复
- **Windows-1252修复** - 修复chardet误检测windows-1252导致的解码错误
- **泰语编码完整支持** - 支持UTF-8、CP874、ISO-8859-11、TIS-620
- **乱码智能识别** - 自动检测和修复常见的UTF-8乱码模式
- **编码可用性检查** - 避免系统不支持的编码导致错误

#### 🌐 Web界面更新
- **版本信息显示** - 显示当前版本v2.1.0和功能更新历史
- **参数说明文档** - 完整的12种特效参数类型说明
- **配置优化显示** - 实时显示轻微特效配置信息

#### 📊 配置优化
- **滤镜强度**: 20-30% → 10-25% (更轻微)
- **对比度范围**: 0.8-1.2 → 0.9-1.1 (更保守)
- **亮度范围**: 0.9-1.1 → 0.95-1.05 (更细微)

### 🎯 v2.0.0 (2025-07-05)
- **防审核技术集成** - 完整的防审核技术系统
- **模糊背景双轨道** - 智能模糊背景处理
- **抽帧处理优化** - 智能抽帧防审核技术
- **镜像翻转集成** - 自动镜像翻转处理

### 📝 v1.6.2 (2025-07-05)
- **泰语字幕编码修复** - 修复泰语字幕编码和时间戳解析问题
- **SRT解析器优化** - 增强多语言字幕文件兼容性
- **编码检测算法优化** - 提高编码检测准确性
